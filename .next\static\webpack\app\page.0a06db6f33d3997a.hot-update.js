"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText, onProgress) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutesWithProgress(meetingText, onProgress);\n        }\n        const prompt = \"\\n你是一个专业的会议纪要分析专家。请仔细分析以下会议内容，并生成结构化的会议纪要。\\n\\n请按照以下要求进行分析：\\n1. 仔细识别会议中的关键信息\\n2. 提取所有参与者姓名和职位\\n3. 总结会议的核心内容和目标\\n4. 识别所有明确的决策和决定\\n5. 提取所有行动项，包括负责人和时间要求\\n6. 分析任务的优先级（根据紧急程度和重要性）\\n\\n请以JSON格式返回结果，包含以下字段：\\n- title: 会议标题（如果没有明确标题，请根据内容生成一个合适的标题）\\n- date: 会议日期（格式：YYYY-MM-DD，如果没有明确日期，使用今天的日期）\\n- participants: 参与者列表（数组，包含姓名和职位信息）\\n- summary: 会议摘要（3-4句话，概括会议的主要内容和成果）\\n- keyDecisions: 关键决策列表（数组，每个决策用简洁明确的语言描述）\\n- actionItems: 行动项列表（数组，每个对象包含：task（具体任务描述）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low））\\n- nextSteps: 下一步计划（数组，列出后续需要进行的工作）\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含markdown代码块标记或其他额外文本。JSON应该直接开始和结束。\\n\");\n        try {\n            var _response_body;\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在连接 AI 服务...', '');\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: true\n                })\n            });\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('AI 正在分析会议内容...', '');\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在处理 AI 响应...', '');\n            // 处理流式响应\n            let fullResponse = '';\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            const decoder = new TextDecoder();\n            if (reader) {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value);\n                    const lines = chunk.split('\\n').filter((line)=>line.trim());\n                    for (const line of lines){\n                        try {\n                            const data = JSON.parse(line);\n                            if (data.response) {\n                                fullResponse += data.response;\n                                onProgress === null || onProgress === void 0 ? void 0 : onProgress('AI 正在生成内容...', fullResponse.slice(0, 100) + '...');\n                            }\n                            if (data.done) break;\n                        } catch (e) {\n                        // 忽略解析错误的行\n                        }\n                    }\n                }\n            } else {\n                // 回退到非流式响应\n                const data = await response.json();\n                fullResponse = data.response;\n            }\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在解析生成结果...', '');\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = fullResponse.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    async generateDemoMinutesWithProgress(meetingText, onProgress) {\n        const steps = [\n            {\n                step: '正在分析会议内容...',\n                delay: 800\n            },\n            {\n                step: '提取参与者信息...',\n                delay: 600\n            },\n            {\n                step: '识别关键决策...',\n                delay: 700\n            },\n            {\n                step: '分析行动项...',\n                delay: 900\n            },\n            {\n                step: '生成会议摘要...',\n                delay: 500\n            },\n            {\n                step: '整理最终结果...',\n                delay: 400\n            }\n        ];\n        for (const { step, delay } of steps){\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress(step, '');\n            await new Promise((resolve)=>setTimeout(resolve, delay));\n        }\n        return this.generateDemoMinutes(meetingText);\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});