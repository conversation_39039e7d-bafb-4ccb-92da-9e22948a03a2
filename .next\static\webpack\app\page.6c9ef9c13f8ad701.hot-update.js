"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/OllamaStatus.tsx":
/*!*****************************************!*\
  !*** ./src/components/OllamaStatus.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OllamaStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ollama */ \"(app-pages-browser)/./src/lib/ollama.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction OllamaStatus(param) {\n    let { demoMode = false } = param;\n    _s();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"OllamaStatus.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_4__.OllamaService()\n    }[\"OllamaStatus.useState\"]);\n    const checkConnection = async ()=>{\n        setIsChecking(true);\n        try {\n            const connected = await ollamaService.checkConnection();\n            setIsConnected(connected);\n            if (connected) {\n                const availableModels = await ollamaService.listModels();\n                setModels(availableModels);\n            } else {\n                setModels([]);\n            }\n        } catch (error) {\n            setIsConnected(false);\n            setModels([]);\n        } finally{\n            setIsChecking(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OllamaStatus.useEffect\": ()=>{\n            checkConnection();\n        }\n    }[\"OllamaStatus.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        \"Ollama 服务状态\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: checkConnection,\n                            disabled: isChecking,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"ml-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2 \".concat(isChecking ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                \"刷新\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isConnected === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4 bg-gray-300 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this) : isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: isConnected === null ? '检查连接中...' : isConnected ? 'Ollama 服务已连接' : 'Ollama 服务未连接'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        isConnected && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: \"可用模型:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md\",\n                                            children: model\n                                        }, model, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this),\n                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-md p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium mb-2\",\n                                    children: \"如何启动 Ollama:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal list-inside space-y-1 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"确保已安装 Ollama: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"curl -fsSL https://ollama.ai/install.sh | sh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"启动 Ollama 服务: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"ollama serve\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"下载推荐模型: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"ollama pull llama3.1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 29\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(OllamaStatus, \"1hb+EvagXS0eceXemLWvvp2pcoE=\");\n_c = OllamaStatus;\nvar _c;\n$RefreshReg$(_c, \"OllamaStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OllamaStatus.tsx\n"));

/***/ })

});