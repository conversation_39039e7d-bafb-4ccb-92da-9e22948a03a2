"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText, onProgress) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutesWithProgress(meetingText, onProgress);\n        }\n        const prompt = \"\\n你是一个专业的会议纪要分析专家。请仔细分析以下会议内容，并生成结构化的会议纪要。\\n\\n请按照以下要求进行分析：\\n1. 仔细识别会议中的关键信息\\n2. 提取所有参与者姓名和职位\\n3. 总结会议的核心内容和目标\\n4. 识别所有明确的决策和决定\\n5. 提取所有行动项，包括负责人和时间要求\\n6. 分析任务的优先级（根据紧急程度和重要性）\\n\\n请以JSON格式返回结果，包含以下字段：\\n- title: 会议标题（如果没有明确标题，请根据内容生成一个合适的标题）\\n- date: 会议日期（格式：YYYY-MM-DD，如果没有明确日期，使用今天的日期）\\n- participants: 参与者列表（数组，包含姓名和职位信息）\\n- summary: 会议摘要（3-4句话，概括会议的主要内容和成果）\\n- keyDecisions: 关键决策列表（数组，每个决策用简洁明确的语言描述）\\n- actionItems: 行动项列表（数组，每个对象包含：task（具体任务描述）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low））\\n- nextSteps: 下一步计划（数组，列出后续需要进行的工作）\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含markdown代码块标记或其他额外文本。JSON应该直接开始和结束。\\n\");\n        try {\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在连接 AI 服务...', '');\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: true\n                })\n            });\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('AI 正在分析会议内容...', '');\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在处理 AI 响应...', '');\n            const data = await response.json();\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在解析生成结果...', '');\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    async generateDemoMinutesWithProgress(meetingText, onProgress) {\n        const steps = [\n            {\n                step: '正在分析会议内容...',\n                delay: 800\n            },\n            {\n                step: '提取参与者信息...',\n                delay: 600\n            },\n            {\n                step: '识别关键决策...',\n                delay: 700\n            },\n            {\n                step: '分析行动项...',\n                delay: 900\n            },\n            {\n                step: '生成会议摘要...',\n                delay: 500\n            },\n            {\n                step: '整理最终结果...',\n                delay: 400\n            }\n        ];\n        for (const { step, delay } of steps){\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress(step, '');\n            await new Promise((resolve)=>setTimeout(resolve, delay));\n        }\n        return this.generateDemoMinutes(meetingText);\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});