<!DOCTYPE html>
<html>
<head>
    <title>Ollama 连接测试</title>
</head>
<body>
    <h1>Ollama 连接测试</h1>
    <button onclick="testConnection()">测试连接</button>
    <div id="result"></div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试连接...';
            
            try {
                const response = await fetch('http://localhost:11434/api/tags');
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3>✅ 连接成功！</h3>
                        <p>可用模型数量: ${data.models ? data.models.length : 0}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `❌ 连接失败: ${response.status}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 连接错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
