"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutes(meetingText);\n        }\n        const prompt = \"\\n请分析以下会议内容，并生成结构化的会议纪要。请以JSON格式返回结果，包含以下字段：\\n\\n1. title: 会议标题\\n2. date: 会议日期（如果文本中没有明确日期，使用今天的日期）\\n3. participants: 参与者列表\\n4. summary: 会议摘要（2-3句话）\\n5. keyDecisions: 关键决策列表\\n6. actionItems: 行动项列表，每个包含 task（任务）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low）\\n7. nextSteps: 下一步计划\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含任何其他文本。\\n\");\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvb2xsYW1hLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxnQkFBZ0I7QUF1QlQsTUFBTUE7SUFXWCxNQUFNQyx1QkFBdUJDLFdBQW1CLEVBQTJCO1FBQ3pFLGlCQUFpQjtRQUNqQixJQUFJLElBQUksQ0FBQ0MsUUFBUSxFQUFFO1lBQ2pCLE9BQU8sSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQ0Y7UUFDbEM7UUFDQSxNQUFNRyxTQUFTLGtUQVlMLE9BQVpILGFBQVk7UUFLVixJQUFJO1lBQ0YsTUFBTUksV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDQyxPQUFPLEVBQUMsa0JBQWdCO2dCQUMzREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxPQUFPLElBQUksQ0FBQ0EsS0FBSztvQkFDakJULFFBQVFBO29CQUNSVSxRQUFRO2dCQUNWO1lBQ0Y7WUFFQSxJQUFJLENBQUNULFNBQVNVLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNLHFCQUFxQyxPQUFoQlgsU0FBU1ksTUFBTTtZQUN0RDtZQUVBLE1BQU1DLE9BQXVCLE1BQU1iLFNBQVNjLElBQUk7WUFFaEQsZUFBZTtZQUNmLElBQUk7Z0JBQ0YsTUFBTUMsa0JBQWtCRixLQUFLYixRQUFRLENBQUNnQixJQUFJO2dCQUMxQyx1QkFBdUI7Z0JBQ3ZCLE1BQU1DLFlBQVlGLGdCQUFnQkcsS0FBSyxDQUFDO2dCQUN4QyxNQUFNQyxhQUFhRixZQUFZQSxTQUFTLENBQUMsRUFBRSxHQUFHRjtnQkFFOUMsTUFBTUssaUJBQWlDZCxLQUFLZSxLQUFLLENBQUNGO2dCQUVsRCxTQUFTO2dCQUNULElBQUksQ0FBQ0MsZUFBZUUsS0FBSyxFQUFFRixlQUFlRSxLQUFLLEdBQUc7Z0JBQ2xELElBQUksQ0FBQ0YsZUFBZUcsSUFBSSxFQUFFSCxlQUFlRyxJQUFJLEdBQUcsSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQ3RGLElBQUksQ0FBQ04sZUFBZU8sWUFBWSxFQUFFUCxlQUFlTyxZQUFZLEdBQUcsRUFBRTtnQkFDbEUsSUFBSSxDQUFDUCxlQUFlUSxPQUFPLEVBQUVSLGVBQWVRLE9BQU8sR0FBRztnQkFDdEQsSUFBSSxDQUFDUixlQUFlUyxZQUFZLEVBQUVULGVBQWVTLFlBQVksR0FBRyxFQUFFO2dCQUNsRSxJQUFJLENBQUNULGVBQWVVLFdBQVcsRUFBRVYsZUFBZVUsV0FBVyxHQUFHLEVBQUU7Z0JBQ2hFLElBQUksQ0FBQ1YsZUFBZVcsU0FBUyxFQUFFWCxlQUFlVyxTQUFTLEdBQUcsRUFBRTtnQkFFNUQsT0FBT1g7WUFDVCxFQUFFLE9BQU9ZLFlBQVk7Z0JBQ25CQyxRQUFRQyxLQUFLLENBQUMsdUJBQXVCRjtnQkFDckMsZ0JBQWdCO2dCQUNoQixPQUFPLElBQUksQ0FBQ0cscUJBQXFCLENBQUN2QztZQUNwQztRQUNGLEVBQUUsT0FBT3NDLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsTUFBTSxJQUFJdkIsTUFBTTtRQUNsQjtJQUNGO0lBRVF3QixzQkFBc0J2QyxXQUFtQixFQUFrQjtRQUNqRSxPQUFPO1lBQ0wwQixPQUFPO1lBQ1BDLE1BQU0sSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDNUNDLGNBQWMsRUFBRTtZQUNoQkMsU0FBUztZQUNUQyxjQUFjO2dCQUFDO2FBQVk7WUFDM0JDLGFBQWE7Z0JBQUM7b0JBQ1pNLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLFVBQVU7Z0JBQ1o7YUFBRTtZQUNGUCxXQUFXO2dCQUFDO2FBQWE7UUFDM0I7SUFDRjtJQUVRakMsb0JBQW9CRixXQUFtQixFQUFrQjtRQUMvRCxlQUFlO1FBQ2YsTUFBTStCLGVBQWUsSUFBSSxDQUFDWSxtQkFBbUIsQ0FBQzNDO1FBQzlDLE1BQU00QyxZQUFZLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUM3QztRQUN4QyxNQUFNOEMsVUFBVSxJQUFJLENBQUNDLGNBQWMsQ0FBQy9DO1FBRXBDLE9BQU87WUFDTDBCLE9BQU87WUFDUEMsTUFBTSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM1Q0MsY0FBY0E7WUFDZEMsU0FBUztZQUNUQyxjQUFjVztZQUNkVixhQUFhWTtZQUNiWCxXQUFXO2dCQUFDO2dCQUFZO2dCQUFZO2FBQVk7UUFDbEQ7SUFDRjtJQUVRUSxvQkFBb0JLLElBQVksRUFBWTtRQUNsRCxNQUFNakIsZUFBZSxFQUFFO1FBQ3ZCLE1BQU1rQixXQUFXO1lBQ2Y7WUFDQTtZQUNBO1NBQ0Q7UUFFREEsU0FBU0MsT0FBTyxDQUFDQyxDQUFBQTtZQUNmLE1BQU1DLFVBQVVKLEtBQUsxQixLQUFLLENBQUM2QjtZQUMzQixJQUFJQyxTQUFTO2dCQUNYQSxRQUFRRixPQUFPLENBQUM1QixDQUFBQTtvQkFDZCxNQUFNK0IsUUFBUS9CLE1BQU1RLEtBQUssQ0FBQyxTQUFTd0IsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLbkMsSUFBSTtvQkFDeERXLGFBQWF5QixJQUFJLElBQUlIO2dCQUN2QjtZQUNGO1FBQ0Y7UUFFQSxPQUFPdEIsYUFBYTBCLE1BQU0sR0FBRyxJQUFJMUIsYUFBYTJCLEtBQUssQ0FBQyxHQUFHLEtBQUs7WUFBQztZQUFNO1lBQU07U0FBSztJQUNoRjtJQUVRYixpQkFBaUJHLElBQVksRUFBWTtRQUMvQyxNQUFNSixZQUFZLEVBQUU7UUFDcEIsTUFBTUssV0FBVztZQUNmO1lBQ0E7WUFDQTtTQUNEO1FBRURBLFNBQVNDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDZixNQUFNQyxVQUFVSixLQUFLMUIsS0FBSyxDQUFDNkI7WUFDM0IsSUFBSUMsU0FBUztnQkFDWFIsVUFBVVksSUFBSSxJQUFJSixRQUFRRSxHQUFHLENBQUNoQyxDQUFBQSxRQUFTQSxNQUFNcUMsT0FBTyxDQUFDLGVBQWUsSUFBSXZDLElBQUk7WUFDOUU7UUFDRjtRQUVBLE9BQU93QixVQUFVYSxNQUFNLEdBQUcsSUFBSWIsWUFBWTtZQUFDO1lBQVc7WUFBVTtTQUFTO0lBQzNFO0lBRVFHLGVBQWVDLElBQVksRUFBbUc7UUFDcEksTUFBTUYsVUFBVSxFQUFFO1FBQ2xCLE1BQU1jLGlCQUFpQjtZQUNyQjtZQUNBO1NBQ0Q7UUFFREEsZUFBZVYsT0FBTyxDQUFDQyxDQUFBQTtZQUNyQixNQUFNQyxVQUFVO21CQUFJSixLQUFLYSxRQUFRLENBQUNWO2FBQVM7WUFDM0NDLFFBQVFGLE9BQU8sQ0FBQzVCLENBQUFBO2dCQUNkLElBQUlBLEtBQUssQ0FBQyxFQUFFLElBQUlBLEtBQUssQ0FBQyxFQUFFLEVBQUU7b0JBQ3hCd0IsUUFBUVUsSUFBSSxDQUFDO3dCQUNYaEIsTUFBTWxCLEtBQUssQ0FBQyxFQUFFLENBQUNGLElBQUk7d0JBQ25CcUIsVUFBVW5CLEtBQUssQ0FBQyxFQUFFLENBQUNGLElBQUk7d0JBQ3ZCc0IsVUFBVTtvQkFDWjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxJQUFJSSxRQUFRVyxNQUFNLEtBQUssR0FBRztZQUN4QlgsUUFBUVUsSUFBSSxDQUNWO2dCQUFFaEIsTUFBTTtnQkFBWUMsVUFBVTtnQkFBTUMsVUFBVTtZQUFnQixHQUM5RDtnQkFBRUYsTUFBTTtnQkFBWUMsVUFBVTtnQkFBTUMsVUFBVTtZQUFrQixHQUNoRTtnQkFBRUYsTUFBTTtnQkFBVUMsVUFBVTtnQkFBTUMsVUFBVTtZQUFlO1FBRS9EO1FBRUEsT0FBT0k7SUFDVDtJQUVBLE1BQU1nQixrQkFBb0M7UUFDeEMsSUFBSSxJQUFJLENBQUM3RCxRQUFRLEVBQUUsT0FBTztRQUUxQixJQUFJO1lBQ0YsTUFBTUcsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDQyxPQUFPLEVBQUM7WUFDN0MsT0FBT0YsU0FBU1UsRUFBRTtRQUNwQixFQUFFLFVBQU07WUFDTixPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1pRCxhQUFnQztRQUNwQyxJQUFJO2dCQUtLOUM7WUFKUCxNQUFNYixXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYixJQUFJLENBQUNDLE9BQU8sRUFBQztZQUM3QyxJQUFJLENBQUNGLFNBQVNVLEVBQUUsRUFBRSxPQUFPLEVBQUU7WUFFM0IsTUFBTUcsT0FBTyxNQUFNYixTQUFTYyxJQUFJO1lBQ2hDLE9BQU9ELEVBQUFBLGVBQUFBLEtBQUsrQyxNQUFNLGNBQVgvQyxtQ0FBQUEsYUFBYXFDLEdBQUcsQ0FBQyxDQUFDMUMsUUFBZUEsTUFBTTJDLElBQUksTUFBSyxFQUFFO1FBQzNELEVBQUUsVUFBTTtZQUNOLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUF6TUFVLFlBQVkzRCxVQUFVLHdCQUF3QixFQUFFTSxRQUFRLFNBQVMsRUFBRVgsV0FBVyxLQUFLLENBQUU7UUFDbkYsSUFBSSxDQUFDSyxPQUFPLEdBQUdBO1FBQ2YsSUFBSSxDQUFDTSxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDWCxRQUFRLEdBQUdBO0lBQ2xCO0FBc01GIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcbGliXFxvbGxhbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gT2xsYW1hIEFQSSDpm4bmiJBcbmV4cG9ydCBpbnRlcmZhY2UgT2xsYW1hUmVzcG9uc2Uge1xuICBtb2RlbDogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHJlc3BvbnNlOiBzdHJpbmc7XG4gIGRvbmU6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVldGluZ01pbnV0ZXMge1xuICB0aXRsZTogc3RyaW5nO1xuICBkYXRlOiBzdHJpbmc7XG4gIHBhcnRpY2lwYW50czogc3RyaW5nW107XG4gIHN1bW1hcnk6IHN0cmluZztcbiAga2V5RGVjaXNpb25zOiBzdHJpbmdbXTtcbiAgYWN0aW9uSXRlbXM6IEFycmF5PHtcbiAgICB0YXNrOiBzdHJpbmc7XG4gICAgYXNzaWduZWU6IHN0cmluZztcbiAgICBkZWFkbGluZT86IHN0cmluZztcbiAgICBwcmlvcml0eTogJ2hpZ2gnIHwgJ21lZGl1bScgfCAnbG93JztcbiAgfT47XG4gIG5leHRTdGVwczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBjbGFzcyBPbGxhbWFTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBiYXNlVXJsOiBzdHJpbmc7XG4gIHByaXZhdGUgbW9kZWw6IHN0cmluZztcbiAgcHJpdmF0ZSBkZW1vTW9kZTogYm9vbGVhbjtcblxuICBjb25zdHJ1Y3RvcihiYXNlVXJsID0gJ2h0dHA6Ly9sb2NhbGhvc3Q6MTE0MzQnLCBtb2RlbCA9ICdxd3E6MzJiJywgZGVtb01vZGUgPSBmYWxzZSkge1xuICAgIHRoaXMuYmFzZVVybCA9IGJhc2VVcmw7XG4gICAgdGhpcy5tb2RlbCA9IG1vZGVsO1xuICAgIHRoaXMuZGVtb01vZGUgPSBkZW1vTW9kZTtcbiAgfVxuXG4gIGFzeW5jIGdlbmVyYXRlTWVldGluZ01pbnV0ZXMobWVldGluZ1RleHQ6IHN0cmluZyk6IFByb21pc2U8TWVldGluZ01pbnV0ZXM+IHtcbiAgICAvLyDlpoLmnpzmmK/mvJTnpLrmqKHlvI/vvIzov5Tlm57mqKHmi5/mlbDmja5cbiAgICBpZiAodGhpcy5kZW1vTW9kZSkge1xuICAgICAgcmV0dXJuIHRoaXMuZ2VuZXJhdGVEZW1vTWludXRlcyhtZWV0aW5nVGV4dCk7XG4gICAgfVxuICAgIGNvbnN0IHByb21wdCA9IGBcbuivt+WIhuaekOS7peS4i+S8muiuruWGheWuue+8jOW5tueUn+aIkOe7k+aehOWMlueahOS8muiurue6quimgeOAguivt+S7pUpTT07moLzlvI/ov5Tlm57nu5PmnpzvvIzljIXlkKvku6XkuIvlrZfmrrXvvJpcblxuMS4gdGl0bGU6IOS8muiuruagh+mimFxuMi4gZGF0ZTog5Lya6K6u5pel5pyf77yI5aaC5p6c5paH5pys5Lit5rKh5pyJ5piO56Gu5pel5pyf77yM5L2/55So5LuK5aSp55qE5pel5pyf77yJXG4zLiBwYXJ0aWNpcGFudHM6IOWPguS4juiAheWIl+ihqFxuNC4gc3VtbWFyeTog5Lya6K6u5pGY6KaB77yIMi0z5Y+l6K+d77yJXG41LiBrZXlEZWNpc2lvbnM6IOWFs+mUruWGs+etluWIl+ihqFxuNi4gYWN0aW9uSXRlbXM6IOihjOWKqOmhueWIl+ihqO+8jOavj+S4quWMheWQqyB0YXNr77yI5Lu75Yqh77yJ44CBYXNzaWduZWXvvIjotJ/otKPkurrvvInjgIFkZWFkbGluZe+8iOaIquatouaXpeacn++8jOWPr+mAie+8ieOAgXByaW9yaXR577yI5LyY5YWI57qn77yaaGlnaC9tZWRpdW0vbG9377yJXG43LiBuZXh0U3RlcHM6IOS4i+S4gOatpeiuoeWIklxuXG7kvJrorq7lhoXlrrnvvJpcbiR7bWVldGluZ1RleHR9XG5cbuivt+ehruS/nei/lOWbnueahOaYr+acieaViOeahEpTT07moLzlvI/vvIzkuI3opoHljIXlkKvku7vkvZXlhbbku5bmlofmnKzjgIJcbmA7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2FwaS9nZW5lcmF0ZWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbW9kZWw6IHRoaXMubW9kZWwsXG4gICAgICAgICAgcHJvbXB0OiBwcm9tcHQsXG4gICAgICAgICAgc3RyZWFtOiBmYWxzZSxcbiAgICAgICAgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYE9sbGFtYSBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhOiBPbGxhbWFSZXNwb25zZSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIFxuICAgICAgLy8g5bCd6K+V6Kej5p6QIEpTT04g5ZON5bqUXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjbGVhbmVkUmVzcG9uc2UgPSBkYXRhLnJlc3BvbnNlLnRyaW0oKTtcbiAgICAgICAgLy8g56e76Zmk5Y+v6IO955qEIG1hcmtkb3duIOS7o+eggeWdl+agh+iusFxuICAgICAgICBjb25zdCBqc29uTWF0Y2ggPSBjbGVhbmVkUmVzcG9uc2UubWF0Y2goL2BgYCg/Ompzb24pP1xccyooW1xcc1xcU10qPylcXHMqYGBgLyk7XG4gICAgICAgIGNvbnN0IGpzb25TdHJpbmcgPSBqc29uTWF0Y2ggPyBqc29uTWF0Y2hbMV0gOiBjbGVhbmVkUmVzcG9uc2U7XG4gICAgICAgIFxuICAgICAgICBjb25zdCBtZWV0aW5nTWludXRlczogTWVldGluZ01pbnV0ZXMgPSBKU09OLnBhcnNlKGpzb25TdHJpbmcpO1xuICAgICAgICBcbiAgICAgICAgLy8g6aqM6K+B5b+F6KaB5a2X5q61XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMudGl0bGUpIG1lZXRpbmdNaW51dGVzLnRpdGxlID0gJ+S8muiurue6quimgSc7XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMuZGF0ZSkgbWVldGluZ01pbnV0ZXMuZGF0ZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLnBhcnRpY2lwYW50cykgbWVldGluZ01pbnV0ZXMucGFydGljaXBhbnRzID0gW107XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMuc3VtbWFyeSkgbWVldGluZ01pbnV0ZXMuc3VtbWFyeSA9ICfkvJrorq7mkZjopoHnlJ/miJDkuK0uLi4nO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLmtleURlY2lzaW9ucykgbWVldGluZ01pbnV0ZXMua2V5RGVjaXNpb25zID0gW107XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMuYWN0aW9uSXRlbXMpIG1lZXRpbmdNaW51dGVzLmFjdGlvbkl0ZW1zID0gW107XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMubmV4dFN0ZXBzKSBtZWV0aW5nTWludXRlcy5uZXh0U3RlcHMgPSBbXTtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBtZWV0aW5nTWludXRlcztcbiAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignSlNPTiBwYXJzaW5nIGVycm9yOicsIHBhcnNlRXJyb3IpO1xuICAgICAgICAvLyDlpoLmnpzop6PmnpDlpLHotKXvvIzov5Tlm57ln7rmnKznu5PmnoRcbiAgICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlRmFsbGJhY2tNaW51dGVzKG1lZXRpbmdUZXh0KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignT2xsYW1hIHNlcnZpY2UgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5Xov57mjqXliLAgT2xsYW1hIOacjeWKoeOAguivt+ehruS/nSBPbGxhbWEg5q2j5Zyo6L+Q6KGM5bm25LiU5qih5Z6L5bey5LiL6L2944CCJyk7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBjcmVhdGVGYWxsYmFja01pbnV0ZXMobWVldGluZ1RleHQ6IHN0cmluZyk6IE1lZXRpbmdNaW51dGVzIHtcbiAgICByZXR1cm4ge1xuICAgICAgdGl0bGU6ICfkvJrorq7nuqropoEnLFxuICAgICAgZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICBwYXJ0aWNpcGFudHM6IFtdLFxuICAgICAgc3VtbWFyeTogJ+eUseS6jkFJ5pyN5Yqh5pqC5pe25LiN5Y+v55So77yM6K+35omL5Yqo5pW055CG5Lya6K6u57qq6KaB44CCJyxcbiAgICAgIGtleURlY2lzaW9uczogWyfor7fmiYvliqjmt7vliqDlhbPplK7lhrPnrZYnXSxcbiAgICAgIGFjdGlvbkl0ZW1zOiBbe1xuICAgICAgICB0YXNrOiAn6K+35omL5Yqo5re75Yqg6KGM5Yqo6aG5JyxcbiAgICAgICAgYXNzaWduZWU6ICflvoXliIbphY0nLFxuICAgICAgICBwcmlvcml0eTogJ21lZGl1bScgYXMgY29uc3RcbiAgICAgIH1dLFxuICAgICAgbmV4dFN0ZXBzOiBbJ+ivt+aJi+WKqOa3u+WKoOS4i+S4gOatpeiuoeWIkiddXG4gICAgfTtcbiAgfVxuXG4gIHByaXZhdGUgZ2VuZXJhdGVEZW1vTWludXRlcyhtZWV0aW5nVGV4dDogc3RyaW5nKTogTWVldGluZ01pbnV0ZXMge1xuICAgIC8vIOWfuuS6jui+k+WFpeaWh+acrOeUn+aIkOa8lOekuuaVsOaNrlxuICAgIGNvbnN0IHBhcnRpY2lwYW50cyA9IHRoaXMuZXh0cmFjdFBhcnRpY2lwYW50cyhtZWV0aW5nVGV4dCk7XG4gICAgY29uc3QgZGVjaXNpb25zID0gdGhpcy5leHRyYWN0RGVjaXNpb25zKG1lZXRpbmdUZXh0KTtcbiAgICBjb25zdCBhY3Rpb25zID0gdGhpcy5leHRyYWN0QWN0aW9ucyhtZWV0aW5nVGV4dCk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgdGl0bGU6ICfkvJrorq7nuqropoEgKOa8lOekuuaooeW8jyknLFxuICAgICAgZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICBwYXJ0aWNpcGFudHM6IHBhcnRpY2lwYW50cyxcbiAgICAgIHN1bW1hcnk6ICfov5nmmK/mvJTnpLrmqKHlvI/nlJ/miJDnmoTkvJrorq7mkZjopoHjgILmnKzmrKHkvJrorq7orqjorrrkuobpobnnm67ov5vlsZXjgIHlhbPplK7lhrPnrZblkozlkI7nu63ooYzliqjorqHliJLjgIInLFxuICAgICAga2V5RGVjaXNpb25zOiBkZWNpc2lvbnMsXG4gICAgICBhY3Rpb25JdGVtczogYWN0aW9ucyxcbiAgICAgIG5leHRTdGVwczogWyfnu6fnu63mjqjov5vpobnnm67lvIDlj5EnLCAn5YeG5aSH5LiL5qyh5Lya6K6u6K6u56iLJywgJ+i3n+i/m+ihjOWKqOmhueaJp+ihjOaDheWGtSddXG4gICAgfTtcbiAgfVxuXG4gIHByaXZhdGUgZXh0cmFjdFBhcnRpY2lwYW50cyh0ZXh0OiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgcGFydGljaXBhbnRzID0gW107XG4gICAgY29uc3QgcGF0dGVybnMgPSBbXG4gICAgICAv5Y+C5LiO6ICFW++8mjpdXFxzKihbXuOAglxcbl0rKS9nLFxuICAgICAgL+S4juS8muS6uuWRmFvvvJo6XVxccyooW17jgIJcXG5dKykvZyxcbiAgICAgIC8oW+W8oOadjueOi+i1teWImOmZiOadqOm7hOWRqOWQtF1cXHcrKVvvvIgoXShbXu+8iSldKylb77yJKV0vZ1xuICAgIF07XG5cbiAgICBwYXR0ZXJucy5mb3JFYWNoKHBhdHRlcm4gPT4ge1xuICAgICAgY29uc3QgbWF0Y2hlcyA9IHRleHQubWF0Y2gocGF0dGVybik7XG4gICAgICBpZiAobWF0Y2hlcykge1xuICAgICAgICBtYXRjaGVzLmZvckVhY2gobWF0Y2ggPT4ge1xuICAgICAgICAgIGNvbnN0IG5hbWVzID0gbWF0Y2guc3BsaXQoL1vvvIws44CBXS8pLm1hcChuYW1lID0+IG5hbWUudHJpbSgpKTtcbiAgICAgICAgICBwYXJ0aWNpcGFudHMucHVzaCguLi5uYW1lcyk7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIHBhcnRpY2lwYW50cy5sZW5ndGggPiAwID8gcGFydGljaXBhbnRzLnNsaWNlKDAsIDUpIDogWyflvKDkuIknLCAn5p2O5ZubJywgJ+eOi+S6lCddO1xuICB9XG5cbiAgcHJpdmF0ZSBleHRyYWN0RGVjaXNpb25zKHRleHQ6IHN0cmluZyk6IHN0cmluZ1tdIHtcbiAgICBjb25zdCBkZWNpc2lvbnMgPSBbXTtcbiAgICBjb25zdCBwYXR0ZXJucyA9IFtcbiAgICAgIC/lhrPlrppb77yaOl0oW17jgIJcXG5dKykvZyxcbiAgICAgIC/noa7lrppb77yaOl0oW17jgIJcXG5dKykvZyxcbiAgICAgIC/lkIzmhI9b77yaOl0oW17jgIJcXG5dKykvZ1xuICAgIF07XG5cbiAgICBwYXR0ZXJucy5mb3JFYWNoKHBhdHRlcm4gPT4ge1xuICAgICAgY29uc3QgbWF0Y2hlcyA9IHRleHQubWF0Y2gocGF0dGVybik7XG4gICAgICBpZiAobWF0Y2hlcykge1xuICAgICAgICBkZWNpc2lvbnMucHVzaCguLi5tYXRjaGVzLm1hcChtYXRjaCA9PiBtYXRjaC5yZXBsYWNlKC9eW17vvJo6XStb77yaOl0vLCAnJykudHJpbSgpKSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gZGVjaXNpb25zLmxlbmd0aCA+IDAgPyBkZWNpc2lvbnMgOiBbJ+ehruWumumhueebruaXtumXtOihqCcsICfmibnlh4bpooTnrpfmlrnmoYgnLCAn5ZCM5oSP5oqA5pyv6YCJ5Z6LJ107XG4gIH1cblxuICBwcml2YXRlIGV4dHJhY3RBY3Rpb25zKHRleHQ6IHN0cmluZyk6IEFycmF5PHt0YXNrOiBzdHJpbmc7IGFzc2lnbmVlOiBzdHJpbmc7IGRlYWRsaW5lPzogc3RyaW5nOyBwcmlvcml0eTogJ2hpZ2gnIHwgJ21lZGl1bScgfCAnbG93J30+IHtcbiAgICBjb25zdCBhY3Rpb25zID0gW107XG4gICAgY29uc3QgYWN0aW9uUGF0dGVybnMgPSBbXG4gICAgICAvKFvlvKDmnY7njovotbXliJjpmYjmnajpu4TlkajlkLRdXFx3Kylb77yaOl0oW17jgIJcXG5dKykvZyxcbiAgICAgIC/otJ/otKPkurpb77yaOl0oW17vvIws44CCXFxuXSspW++8jCxdPyhbXuOAglxcbl0qKS9nXG4gICAgXTtcblxuICAgIGFjdGlvblBhdHRlcm5zLmZvckVhY2gocGF0dGVybiA9PiB7XG4gICAgICBjb25zdCBtYXRjaGVzID0gWy4uLnRleHQubWF0Y2hBbGwocGF0dGVybildO1xuICAgICAgbWF0Y2hlcy5mb3JFYWNoKG1hdGNoID0+IHtcbiAgICAgICAgaWYgKG1hdGNoWzFdICYmIG1hdGNoWzJdKSB7XG4gICAgICAgICAgYWN0aW9ucy5wdXNoKHtcbiAgICAgICAgICAgIHRhc2s6IG1hdGNoWzJdLnRyaW0oKSxcbiAgICAgICAgICAgIGFzc2lnbmVlOiBtYXRjaFsxXS50cmltKCksXG4gICAgICAgICAgICBwcmlvcml0eTogJ21lZGl1bScgYXMgY29uc3RcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfSk7XG5cbiAgICBpZiAoYWN0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICAgIGFjdGlvbnMucHVzaChcbiAgICAgICAgeyB0YXNrOiAn5a6M5oiQ6aG555uu5paH5qGj5pW055CGJywgYXNzaWduZWU6ICflvKDkuIknLCBwcmlvcml0eTogJ2hpZ2gnIGFzIGNvbnN0IH0sXG4gICAgICAgIHsgdGFzazogJ+WHhuWkh+S4i+WRqOaxh+aKpeadkOaWmScsIGFzc2lnbmVlOiAn5p2O5ZubJywgcHJpb3JpdHk6ICdtZWRpdW0nIGFzIGNvbnN0IH0sXG4gICAgICAgIHsgdGFzazogJ+i3n+i/m+WuouaIt+WPjemmiCcsIGFzc2lnbmVlOiAn546L5LqUJywgcHJpb3JpdHk6ICdsb3cnIGFzIGNvbnN0IH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGFjdGlvbnM7XG4gIH1cblxuICBhc3luYyBjaGVja0Nvbm5lY3Rpb24oKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgaWYgKHRoaXMuZGVtb01vZGUpIHJldHVybiB0cnVlO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9hcGkvdGFnc2ApO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLm9rO1xuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGxpc3RNb2RlbHMoKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vYXBpL3RhZ3NgKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHJldHVybiBbXTtcbiAgICAgIFxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiBkYXRhLm1vZGVscz8ubWFwKChtb2RlbDogYW55KSA9PiBtb2RlbC5uYW1lKSB8fCBbXTtcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJPbGxhbWFTZXJ2aWNlIiwiZ2VuZXJhdGVNZWV0aW5nTWludXRlcyIsIm1lZXRpbmdUZXh0IiwiZGVtb01vZGUiLCJnZW5lcmF0ZURlbW9NaW51dGVzIiwicHJvbXB0IiwicmVzcG9uc2UiLCJmZXRjaCIsImJhc2VVcmwiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJtb2RlbCIsInN0cmVhbSIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJkYXRhIiwianNvbiIsImNsZWFuZWRSZXNwb25zZSIsInRyaW0iLCJqc29uTWF0Y2giLCJtYXRjaCIsImpzb25TdHJpbmciLCJtZWV0aW5nTWludXRlcyIsInBhcnNlIiwidGl0bGUiLCJkYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJwYXJ0aWNpcGFudHMiLCJzdW1tYXJ5Iiwia2V5RGVjaXNpb25zIiwiYWN0aW9uSXRlbXMiLCJuZXh0U3RlcHMiLCJwYXJzZUVycm9yIiwiY29uc29sZSIsImVycm9yIiwiY3JlYXRlRmFsbGJhY2tNaW51dGVzIiwidGFzayIsImFzc2lnbmVlIiwicHJpb3JpdHkiLCJleHRyYWN0UGFydGljaXBhbnRzIiwiZGVjaXNpb25zIiwiZXh0cmFjdERlY2lzaW9ucyIsImFjdGlvbnMiLCJleHRyYWN0QWN0aW9ucyIsInRleHQiLCJwYXR0ZXJucyIsImZvckVhY2giLCJwYXR0ZXJuIiwibWF0Y2hlcyIsIm5hbWVzIiwibWFwIiwibmFtZSIsInB1c2giLCJsZW5ndGgiLCJzbGljZSIsInJlcGxhY2UiLCJhY3Rpb25QYXR0ZXJucyIsIm1hdGNoQWxsIiwiY2hlY2tDb25uZWN0aW9uIiwibGlzdE1vZGVscyIsIm1vZGVscyIsImNvbnN0cnVjdG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});