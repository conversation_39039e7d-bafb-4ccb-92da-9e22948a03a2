"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/OllamaStatus.tsx":
/*!*****************************************!*\
  !*** ./src/components/OllamaStatus.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OllamaStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ollama */ \"(app-pages-browser)/./src/lib/ollama.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction OllamaStatus(param) {\n    let { demoMode = false } = param;\n    _s();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"OllamaStatus.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_4__.OllamaService()\n    }[\"OllamaStatus.useState\"]);\n    const checkConnection = async ()=>{\n        setIsChecking(true);\n        try {\n            if (demoMode) {\n                setIsConnected(true);\n                setModels([\n                    '演示模式 (无需真实模型)'\n                ]);\n            } else {\n                const connected = await ollamaService.checkConnection();\n                setIsConnected(connected);\n                if (connected) {\n                    const availableModels = await ollamaService.listModels();\n                    setModels(availableModels);\n                } else {\n                    setModels([]);\n                }\n            }\n        } catch (error) {\n            setIsConnected(false);\n            setModels([]);\n        } finally{\n            setIsChecking(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OllamaStatus.useEffect\": ()=>{\n            checkConnection();\n        }\n    }[\"OllamaStatus.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        \"Ollama 服务状态\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: checkConnection,\n                            disabled: isChecking,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"ml-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2 \".concat(isChecking ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                \"刷新\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isConnected === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4 bg-gray-300 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this) : isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: isConnected === null ? '检查连接中...' : isConnected ? 'Ollama 服务已连接' : 'Ollama 服务未连接'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        isConnected && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: \"可用模型:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md\",\n                                            children: model\n                                        }, model, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-md p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium mb-2\",\n                                    children: \"如何启动 Ollama:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal list-inside space-y-1 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"确保已安装 Ollama: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"curl -fsSL https://ollama.ai/install.sh | sh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"启动 Ollama 服务: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"ollama serve\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"下载推荐模型: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"ollama pull llama3.1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 29\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(OllamaStatus, \"1hb+EvagXS0eceXemLWvvp2pcoE=\");\n_c = OllamaStatus;\nvar _c;\n$RefreshReg$(_c, \"OllamaStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OllamaStatus.tsx\n"));

/***/ })

});