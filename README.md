# 智能会议纪要生成器

一个基于 Next.js 15 和 Ollama 的本地 AI 会议纪要生成应用，可以自动将会议内容转换为结构化的会议纪要。

## ✨ 功能特性

- 🤖 **本地 AI 处理** - 使用 Ollama 在本地运行 AI 模型，保护数据隐私
- 📝 **智能内容分析** - 自动提取会议摘要、关键决策和行动项
- 📋 **结构化输出** - 生成包含参与者、决策、任务分配的完整纪要
- 💾 **文件支持** - 支持文本输入和 .txt 文件上传
- 📱 **响应式设计** - 基于 Tailwind CSS 的现代化界面
- ⬇️ **导出功能** - 支持导出为 Markdown 格式

## 🛠️ 技术栈

- **前端框架**: Next.js 15
- **样式**: Tailwind CSS v4
- **UI 组件**: shadcn/ui
- **AI 模型**: Ollama (本地运行)
- **语言**: TypeScript

## 📋 系统要求

- Node.js 18+ 
- <PERSON><PERSON><PERSON> (用于本地 AI 推理)

## 🚀 快速开始

### 1. 安装 Ollama

```bash
# macOS/Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# 从 https://ollama.ai 下载安装包
```

### 2. 启动 Ollama 服务

```bash
ollama serve
```

### 3. 下载推荐的 AI 模型

```bash
# 推荐使用 qwq:32b (约 20GB，推理能力强)
ollama pull qwq:32b

# 或者使用更小的模型
ollama pull qwen2.5:7b
ollama pull llama3.1:8b
```

### 4. 安装项目依赖

```bash
npm install
```

### 5. 启动开发服务器

```bash
npm run dev
```

### 6. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📖 使用说明

1. **检查 Ollama 状态** - 确保页面顶部显示 "Ollama 服务已连接"
2. **输入会议内容** - 可以直接粘贴文本或上传 .txt 文件
3. **生成纪要** - 点击"生成会议纪要"按钮
4. **查看结果** - 右侧将显示结构化的会议纪要
5. **导出文件** - 点击"下载"按钮保存为 Markdown 文件

## 🌐 华为内网环境配置

如果在华为内网环境中遇到网络问题，可以尝试：

### 方法 1: 配置代理下载模型
```bash
# 设置代理环境变量
set https_proxy=http://d84190609:<EMAIL>:8080
set http_proxy=http://d84190609:<EMAIL>:8080

# 下载 qwq:32b 模型（约 20GB）
ollama pull qwq:32b
```

### 方法 2: 使用演示模式
- 在应用界面勾选"演示模式（无需 Ollama）"
- 可以体验完整功能，无需下载模型
- 演示模式包含智能文本分析和结构化输出

### 方法 3: 使用更小的模型
```bash
ollama pull qwen2.5:0.5b  # 约 400MB
ollama pull qwen2.5:7b    # 约 4GB
```

## 📁 项目结构

```
├── src/
│   ├── app/                 # Next.js 应用路由
│   ├── components/          # React 组件
│   │   ├── ui/             # shadcn/ui 基础组件
│   │   ├── MeetingMinutesGenerator.tsx
│   │   └── OllamaStatus.tsx
│   └── lib/                # 工具库
│       ├── ollama.ts       # Ollama API 集成
│       └── utils.ts        # 通用工具函数
├── example-meeting.txt     # 示例会议文本
└── README.md
```

## 🔧 配置选项

可以在 `src/lib/ollama.ts` 中修改以下配置：

- **Ollama 服务地址**: 默认 `http://localhost:11434`
- **AI 模型**: 默认 `qwq:32b`

## 🎯 生成的纪要结构

- **会议标题和日期**
- **参与者列表**
- **会议摘要**
- **关键决策**
- **行动项** (包含任务、负责人、优先级、截止日期)
- **下一步计划**

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [shadcn/ui](https://ui.shadcn.com/) - UI 组件库
- [Ollama](https://ollama.ai/) - 本地 AI 运行时
- [Lucide](https://lucide.dev/) - 图标库
