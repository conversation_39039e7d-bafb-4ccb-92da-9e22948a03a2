// Ollama API 集成
export interface OllamaResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
}

export interface MeetingMinutes {
  title: string;
  date: string;
  participants: string[];
  summary: string;
  keyDecisions: string[];
  actionItems: Array<{
    task: string;
    assignee: string;
    deadline?: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  nextSteps: string[];
}

export class OllamaService {
  private baseUrl: string;
  private model: string;
  private demoMode: boolean;

  constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false) {
    this.baseUrl = baseUrl;
    this.model = model;
    this.demoMode = demoMode;
  }

  async generateMeetingMinutes(
    meetingText: string,
    onProgress?: (step: string, content: string) => void
  ): Promise<MeetingMinutes> {
    // 如果是演示模式，返回模拟数据
    if (this.demoMode) {
      return this.generateDemoMinutesWithProgress(meetingText, onProgress);
    }
    const prompt = `
你是一个专业的会议纪要分析专家。请仔细分析以下会议内容，并生成结构化的会议纪要。

请按照以下要求进行分析：
1. 仔细识别会议中的关键信息
2. 提取所有参与者姓名和职位
3. 总结会议的核心内容和目标
4. 识别所有明确的决策和决定
5. 提取所有行动项，包括负责人和时间要求
6. 分析任务的优先级（根据紧急程度和重要性）

请以JSON格式返回结果，包含以下字段：
- title: 会议标题（如果没有明确标题，请根据内容生成一个合适的标题）
- date: 会议日期（格式：YYYY-MM-DD，如果没有明确日期，使用今天的日期）
- participants: 参与者列表（数组，包含姓名和职位信息）
- summary: 会议摘要（3-4句话，概括会议的主要内容和成果）
- keyDecisions: 关键决策列表（数组，每个决策用简洁明确的语言描述）
- actionItems: 行动项列表（数组，每个对象包含：task（具体任务描述）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low））
- nextSteps: 下一步计划（数组，列出后续需要进行的工作）

会议内容：
${meetingText}

请确保返回的是有效的JSON格式，不要包含markdown代码块标记或其他额外文本。JSON应该直接开始和结束。
`;

    try {
      onProgress?.('正在连接 AI 服务...', '');

      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          prompt: prompt,
          stream: true,
        }),
      });

      onProgress?.('AI 正在分析会议内容...', '');

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`);
      }

      onProgress?.('正在处理 AI 响应...', '');

      // 处理流式响应
      let fullResponse = '';
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n').filter(line => line.trim());

          for (const line of lines) {
            try {
              const data = JSON.parse(line);
              if (data.response) {
                fullResponse += data.response;
                onProgress?.('AI 正在生成内容...', fullResponse.slice(0, 100) + '...');
              }
              if (data.done) break;
            } catch (e) {
              // 忽略解析错误的行
            }
          }
        }
      } else {
        // 回退到非流式响应
        const data: OllamaResponse = await response.json();
        fullResponse = data.response;
      }

      onProgress?.('正在解析生成结果...', '');
      // 尝试解析 JSON 响应
      try {
        const cleanedResponse = fullResponse.trim();
        // 移除可能的 markdown 代码块标记
        const jsonMatch = cleanedResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
        const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;

        const meetingMinutes: MeetingMinutes = JSON.parse(jsonString);
        
        // 验证必要字段
        if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';
        if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];
        if (!meetingMinutes.participants) meetingMinutes.participants = [];
        if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';
        if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];
        if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];
        if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];
        
        return meetingMinutes;
      } catch (parseError) {
        console.error('JSON parsing error:', parseError);
        // 如果解析失败，返回基本结构
        return this.createFallbackMinutes(meetingText);
      }
    } catch (error) {
      console.error('Ollama service error:', error);
      throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');
    }
  }

  private createFallbackMinutes(meetingText: string): MeetingMinutes {
    return {
      title: '会议纪要',
      date: new Date().toISOString().split('T')[0],
      participants: [],
      summary: '由于AI服务暂时不可用，请手动整理会议纪要。',
      keyDecisions: ['请手动添加关键决策'],
      actionItems: [{
        task: '请手动添加行动项',
        assignee: '待分配',
        priority: 'medium' as const
      }],
      nextSteps: ['请手动添加下一步计划']
    };
  }

  private async generateDemoMinutesWithProgress(
    meetingText: string,
    onProgress?: (step: string, content: string) => void
  ): Promise<MeetingMinutes> {
    const steps = [
      { step: '正在分析会议内容...', delay: 800 },
      { step: '提取参与者信息...', delay: 600 },
      { step: '识别关键决策...', delay: 700 },
      { step: '分析行动项...', delay: 900 },
      { step: '生成会议摘要...', delay: 500 },
      { step: '整理最终结果...', delay: 400 }
    ];

    for (const { step, delay } of steps) {
      onProgress?.(step, '');
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    return this.generateDemoMinutes(meetingText);
  }

  private generateDemoMinutes(meetingText: string): MeetingMinutes {
    // 基于输入文本生成演示数据
    const participants = this.extractParticipants(meetingText);
    const decisions = this.extractDecisions(meetingText);
    const actions = this.extractActions(meetingText);

    return {
      title: '会议纪要 (演示模式)',
      date: new Date().toISOString().split('T')[0],
      participants: participants,
      summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',
      keyDecisions: decisions,
      actionItems: actions,
      nextSteps: ['继续推进项目开发', '准备下次会议议程', '跟进行动项执行情况']
    };
  }

  private extractParticipants(text: string): string[] {
    const participants = [];
    const patterns = [
      /参与者[：:]\s*([^。\n]+)/g,
      /与会人员[：:]\s*([^。\n]+)/g,
      /([张李王赵刘陈杨黄周吴]\w+)[（(]([^）)]+)[）)]/g
    ];

    patterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const names = match.split(/[，,、]/).map(name => name.trim());
          participants.push(...names);
        });
      }
    });

    return participants.length > 0 ? participants.slice(0, 5) : ['张三', '李四', '王五'];
  }

  private extractDecisions(text: string): string[] {
    const decisions = [];
    const patterns = [
      /决定[：:]([^。\n]+)/g,
      /确定[：:]([^。\n]+)/g,
      /同意[：:]([^。\n]+)/g
    ];

    patterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        decisions.push(...matches.map(match => match.replace(/^[^：:]+[：:]/, '').trim()));
      }
    });

    return decisions.length > 0 ? decisions : ['确定项目时间表', '批准预算方案', '同意技术选型'];
  }

  private extractActions(text: string): Array<{task: string; assignee: string; deadline?: string; priority: 'high' | 'medium' | 'low'}> {
    const actions = [];
    const actionPatterns = [
      /([张李王赵刘陈杨黄周吴]\w+)[：:]([^。\n]+)/g,
      /负责人[：:]([^，,。\n]+)[，,]?([^。\n]*)/g
    ];

    actionPatterns.forEach(pattern => {
      const matches = [...text.matchAll(pattern)];
      matches.forEach(match => {
        if (match[1] && match[2]) {
          actions.push({
            task: match[2].trim(),
            assignee: match[1].trim(),
            priority: 'medium' as const
          });
        }
      });
    });

    if (actions.length === 0) {
      actions.push(
        { task: '完成项目文档整理', assignee: '张三', priority: 'high' as const },
        { task: '准备下周汇报材料', assignee: '李四', priority: 'medium' as const },
        { task: '跟进客户反馈', assignee: '王五', priority: 'low' as const }
      );
    }

    return actions;
  }

  async checkConnection(): Promise<boolean> {
    if (this.demoMode) return true;

    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      return response.ok;
    } catch {
      return false;
    }
  }

  async listModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      if (!response.ok) return [];
      
      const data = await response.json();
      return data.models?.map((model: any) => model.name) || [];
    } catch {
      return [];
    }
  }
}
