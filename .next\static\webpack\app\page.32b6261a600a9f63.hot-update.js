"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MeetingMinutesGenerator.tsx":
/*!****************************************************!*\
  !*** ./src/components/MeetingMinutesGenerator.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MeetingMinutesGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/ollama */ \"(app-pages-browser)/./src/lib/ollama.ts\");\n/* harmony import */ var _components_OllamaStatus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/OllamaStatus */ \"(app-pages-browser)/./src/components/OllamaStatus.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MeetingMinutesGenerator() {\n    _s();\n    const [meetingText, setMeetingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [meetingMinutes, setMeetingMinutes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [demoMode, setDemoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('qwq:32b');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"MeetingMinutesGenerator.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_6__.OllamaService('http://localhost:11434', selectedModel, demoMode)\n    }[\"MeetingMinutesGenerator.useState\"]);\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file && file.type === 'text/plain') {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const content = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setMeetingText(content);\n            };\n            reader.readAsText(file);\n        } else {\n            setError('请上传 .txt 格式的文件');\n        }\n    };\n    const generateMinutes = async ()=>{\n        if (!meetingText.trim()) {\n            setError('请输入会议内容');\n            return;\n        }\n        setIsGenerating(true);\n        setError(null);\n        try {\n            // 创建新的服务实例以使用当前的演示模式和模型设置\n            const service = new _lib_ollama__WEBPACK_IMPORTED_MODULE_6__.OllamaService('http://localhost:11434', selectedModel, demoMode);\n            const minutes = await service.generateMeetingMinutes(meetingText);\n            setMeetingMinutes(minutes);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : '生成会议纪要时发生错误');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const downloadMinutes = ()=>{\n        if (!meetingMinutes) return;\n        const content = \"\\n# \".concat(meetingMinutes.title, \"\\n\\n**日期：** \").concat(meetingMinutes.date, \"\\n\\n**参与者：** \").concat(meetingMinutes.participants.join(', '), \"\\n\\n## 会议摘要\\n\").concat(meetingMinutes.summary, \"\\n\\n## 关键决策\\n\").concat(meetingMinutes.keyDecisions.map((decision)=>\"- \".concat(decision)).join('\\n'), \"\\n\\n## 行动项\\n\").concat(meetingMinutes.actionItems.map((item)=>\"- **\".concat(item.task, \"** (负责人: \").concat(item.assignee, \", 优先级: \").concat(item.priority).concat(item.deadline ? \", 截止日期: \".concat(item.deadline) : '', \")\")).join('\\n'), \"\\n\\n## 下一步计划\\n\").concat(meetingMinutes.nextSteps.map((step)=>\"- \".concat(step)).join('\\n'), \"\\n\");\n        const blob = new Blob([\n            content\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"\".concat(meetingMinutes.title.replace(/\\s+/g, '_'), \"_\").concat(meetingMinutes.date, \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'low':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"智能会议纪要生成器\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"使用本地 AI 技术自动生成结构化的会议纪要\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-6 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center gap-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: demoMode,\n                                        onChange: (e)=>setDemoMode(e.target.checked),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"演示模式（无需 Ollama）\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            !demoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"model-select\",\n                                        children: \"AI 模型:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"model-select\",\n                                        value: selectedModel,\n                                        onChange: (e)=>setSelectedModel(e.target.value),\n                                        className: \"px-2 py-1 border border-gray-300 rounded text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwq:32b\",\n                                                children: \"qwq:32b (推荐)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwen2.5:7b\",\n                                                children: \"qwen2.5:7b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwen2.5:0.5b\",\n                                                children: \"qwen2.5:0.5b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"llama3.1:8b\",\n                                                children: \"llama3.1:8b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"llama3.2:3b\",\n                                                children: \"llama3.2:3b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OllamaStatus__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                demoMode: demoMode\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"会议内容输入\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"粘贴会议文本或上传文本文件\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"上传文本文件\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"file-upload\",\n                                                type: \"file\",\n                                                accept: \".txt\",\n                                                onChange: handleFileUpload,\n                                                className: \"cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                placeholder: \"在此粘贴会议内容，包括讨论要点、决策和任务分配...\",\n                                                value: meetingText,\n                                                onChange: (e)=>setMeetingText(e.target.value),\n                                                className: \"min-h-[300px] resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 right-2 text-xs text-gray-400\",\n                                                children: [\n                                                    meetingText.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: generateMinutes,\n                                        disabled: isGenerating || !meetingText.trim(),\n                                        className: \"w-full\",\n                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"正在生成纪要...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"生成会议纪要\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"生成的会议纪要\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            meetingMinutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: downloadMinutes,\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"下载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"AI 生成的结构化会议纪要\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: meetingMinutes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: meetingMinutes.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"日期: \",\n                                                        meetingMinutes.date\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                meetingMinutes.participants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"参与者: \",\n                                                        meetingMinutes.participants.join(', ')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"会议摘要\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 bg-gray-50 p-3 rounded-md\",\n                                                    children: meetingMinutes.summary\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this),\n                                        meetingMinutes.keyDecisions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"关键决策\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.keyDecisions.map((decision, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                decision\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.actionItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"行动项\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: meetingMinutes.actionItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-md p-3 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: item.task\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 266,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(item.priority)),\n                                                                            children: item.priority\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1 text-xs text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"负责人: \",\n                                                                                item.assignee\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        item.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                \"截止: \",\n                                                                                item.deadline\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 47\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.nextSteps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"下一步计划\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.nextSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                step\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"在左侧输入会议内容后，点击生成按钮\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(MeetingMinutesGenerator, \"AVlJXhhej8eVqsGbhquD49TuZig=\");\n_c = MeetingMinutesGenerator;\nvar _c;\n$RefreshReg$(_c, \"MeetingMinutesGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MeetingMinutesGenerator.tsx\n"));

/***/ })

});