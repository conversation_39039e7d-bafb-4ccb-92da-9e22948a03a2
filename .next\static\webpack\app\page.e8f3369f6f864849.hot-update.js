"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText) {\n        const prompt = \"\\n请分析以下会议内容，并生成结构化的会议纪要。请以JSON格式返回结果，包含以下字段：\\n\\n1. title: 会议标题\\n2. date: 会议日期（如果文本中没有明确日期，使用今天的日期）\\n3. participants: 参与者列表\\n4. summary: 会议摘要（2-3句话）\\n5. keyDecisions: 关键决策列表\\n6. actionItems: 行动项列表，每个包含 task（任务）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low）\\n7. nextSteps: 下一步计划\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含任何其他文本。\\n\");\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    async checkConnection() {\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'llama3.2:3b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvb2xsYW1hLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxnQkFBZ0I7QUF1QlQsTUFBTUE7SUFXWCxNQUFNQyx1QkFBdUJDLFdBQW1CLEVBQTJCO1FBQ3pFLE1BQU1DLFNBQVMsa1RBWUwsT0FBWkQsYUFBWTtRQUtWLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYixJQUFJLENBQUNDLE9BQU8sRUFBQyxrQkFBZ0I7Z0JBQzNEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLE9BQU8sSUFBSSxDQUFDQSxLQUFLO29CQUNqQlQsUUFBUUE7b0JBQ1JVLFFBQVE7Z0JBQ1Y7WUFDRjtZQUVBLElBQUksQ0FBQ1QsU0FBU1UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0scUJBQXFDLE9BQWhCWCxTQUFTWSxNQUFNO1lBQ3REO1lBRUEsTUFBTUMsT0FBdUIsTUFBTWIsU0FBU2MsSUFBSTtZQUVoRCxlQUFlO1lBQ2YsSUFBSTtnQkFDRixNQUFNQyxrQkFBa0JGLEtBQUtiLFFBQVEsQ0FBQ2dCLElBQUk7Z0JBQzFDLHVCQUF1QjtnQkFDdkIsTUFBTUMsWUFBWUYsZ0JBQWdCRyxLQUFLLENBQUM7Z0JBQ3hDLE1BQU1DLGFBQWFGLFlBQVlBLFNBQVMsQ0FBQyxFQUFFLEdBQUdGO2dCQUU5QyxNQUFNSyxpQkFBaUNkLEtBQUtlLEtBQUssQ0FBQ0Y7Z0JBRWxELFNBQVM7Z0JBQ1QsSUFBSSxDQUFDQyxlQUFlRSxLQUFLLEVBQUVGLGVBQWVFLEtBQUssR0FBRztnQkFDbEQsSUFBSSxDQUFDRixlQUFlRyxJQUFJLEVBQUVILGVBQWVHLElBQUksR0FBRyxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDdEYsSUFBSSxDQUFDTixlQUFlTyxZQUFZLEVBQUVQLGVBQWVPLFlBQVksR0FBRyxFQUFFO2dCQUNsRSxJQUFJLENBQUNQLGVBQWVRLE9BQU8sRUFBRVIsZUFBZVEsT0FBTyxHQUFHO2dCQUN0RCxJQUFJLENBQUNSLGVBQWVTLFlBQVksRUFBRVQsZUFBZVMsWUFBWSxHQUFHLEVBQUU7Z0JBQ2xFLElBQUksQ0FBQ1QsZUFBZVUsV0FBVyxFQUFFVixlQUFlVSxXQUFXLEdBQUcsRUFBRTtnQkFDaEUsSUFBSSxDQUFDVixlQUFlVyxTQUFTLEVBQUVYLGVBQWVXLFNBQVMsR0FBRyxFQUFFO2dCQUU1RCxPQUFPWDtZQUNULEVBQUUsT0FBT1ksWUFBWTtnQkFDbkJDLFFBQVFDLEtBQUssQ0FBQyx1QkFBdUJGO2dCQUNyQyxnQkFBZ0I7Z0JBQ2hCLE9BQU8sSUFBSSxDQUFDRyxxQkFBcUIsQ0FBQ3JDO1lBQ3BDO1FBQ0YsRUFBRSxPQUFPb0MsT0FBTztZQUNkRCxRQUFRQyxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNLElBQUl2QixNQUFNO1FBQ2xCO0lBQ0Y7SUFFUXdCLHNCQUFzQnJDLFdBQW1CLEVBQWtCO1FBQ2pFLE9BQU87WUFDTHdCLE9BQU87WUFDUEMsTUFBTSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM1Q0MsY0FBYyxFQUFFO1lBQ2hCQyxTQUFTO1lBQ1RDLGNBQWM7Z0JBQUM7YUFBWTtZQUMzQkMsYUFBYTtnQkFBQztvQkFDWk0sTUFBTTtvQkFDTkMsVUFBVTtvQkFDVkMsVUFBVTtnQkFDWjthQUFFO1lBQ0ZQLFdBQVc7Z0JBQUM7YUFBYTtRQUMzQjtJQUNGO0lBRUEsTUFBTVEsa0JBQW9DO1FBQ3hDLElBQUk7WUFDRixNQUFNdkMsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDQyxPQUFPLEVBQUM7WUFDN0MsT0FBT0YsU0FBU1UsRUFBRTtRQUNwQixFQUFFLFVBQU07WUFDTixPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU04QixhQUFnQztRQUNwQyxJQUFJO2dCQUtLM0I7WUFKUCxNQUFNYixXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYixJQUFJLENBQUNDLE9BQU8sRUFBQztZQUM3QyxJQUFJLENBQUNGLFNBQVNVLEVBQUUsRUFBRSxPQUFPLEVBQUU7WUFFM0IsTUFBTUcsT0FBTyxNQUFNYixTQUFTYyxJQUFJO1lBQ2hDLE9BQU9ELEVBQUFBLGVBQUFBLEtBQUs0QixNQUFNLGNBQVg1QixtQ0FBQUEsYUFBYTZCLEdBQUcsQ0FBQyxDQUFDbEMsUUFBZUEsTUFBTW1DLElBQUksTUFBSyxFQUFFO1FBQzNELEVBQUUsVUFBTTtZQUNOLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUE1R0FDLFlBQVkxQyxVQUFVLHdCQUF3QixFQUFFTSxRQUFRLGFBQWEsRUFBRXFDLFdBQVcsS0FBSyxDQUFFO1FBQ3ZGLElBQUksQ0FBQzNDLE9BQU8sR0FBR0E7UUFDZixJQUFJLENBQUNNLEtBQUssR0FBR0E7UUFDYixJQUFJLENBQUNxQyxRQUFRLEdBQUdBO0lBQ2xCO0FBeUdGIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcbGliXFxvbGxhbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gT2xsYW1hIEFQSSDpm4bmiJBcbmV4cG9ydCBpbnRlcmZhY2UgT2xsYW1hUmVzcG9uc2Uge1xuICBtb2RlbDogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHJlc3BvbnNlOiBzdHJpbmc7XG4gIGRvbmU6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVldGluZ01pbnV0ZXMge1xuICB0aXRsZTogc3RyaW5nO1xuICBkYXRlOiBzdHJpbmc7XG4gIHBhcnRpY2lwYW50czogc3RyaW5nW107XG4gIHN1bW1hcnk6IHN0cmluZztcbiAga2V5RGVjaXNpb25zOiBzdHJpbmdbXTtcbiAgYWN0aW9uSXRlbXM6IEFycmF5PHtcbiAgICB0YXNrOiBzdHJpbmc7XG4gICAgYXNzaWduZWU6IHN0cmluZztcbiAgICBkZWFkbGluZT86IHN0cmluZztcbiAgICBwcmlvcml0eTogJ2hpZ2gnIHwgJ21lZGl1bScgfCAnbG93JztcbiAgfT47XG4gIG5leHRTdGVwczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBjbGFzcyBPbGxhbWFTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBiYXNlVXJsOiBzdHJpbmc7XG4gIHByaXZhdGUgbW9kZWw6IHN0cmluZztcbiAgcHJpdmF0ZSBkZW1vTW9kZTogYm9vbGVhbjtcblxuICBjb25zdHJ1Y3RvcihiYXNlVXJsID0gJ2h0dHA6Ly9sb2NhbGhvc3Q6MTE0MzQnLCBtb2RlbCA9ICdsbGFtYTMuMjozYicsIGRlbW9Nb2RlID0gZmFsc2UpIHtcbiAgICB0aGlzLmJhc2VVcmwgPSBiYXNlVXJsO1xuICAgIHRoaXMubW9kZWwgPSBtb2RlbDtcbiAgICB0aGlzLmRlbW9Nb2RlID0gZGVtb01vZGU7XG4gIH1cblxuICBhc3luYyBnZW5lcmF0ZU1lZXRpbmdNaW51dGVzKG1lZXRpbmdUZXh0OiBzdHJpbmcpOiBQcm9taXNlPE1lZXRpbmdNaW51dGVzPiB7XG4gICAgY29uc3QgcHJvbXB0ID0gYFxu6K+35YiG5p6Q5Lul5LiL5Lya6K6u5YaF5a6577yM5bm255Sf5oiQ57uT5p6E5YyW55qE5Lya6K6u57qq6KaB44CC6K+35LulSlNPTuagvOW8j+i/lOWbnue7k+aenO+8jOWMheWQq+S7peS4i+Wtl+aute+8mlxuXG4xLiB0aXRsZTog5Lya6K6u5qCH6aKYXG4yLiBkYXRlOiDkvJrorq7ml6XmnJ/vvIjlpoLmnpzmlofmnKzkuK3msqHmnInmmI7noa7ml6XmnJ/vvIzkvb/nlKjku4rlpKnnmoTml6XmnJ/vvIlcbjMuIHBhcnRpY2lwYW50czog5Y+C5LiO6ICF5YiX6KGoXG40LiBzdW1tYXJ5OiDkvJrorq7mkZjopoHvvIgyLTPlj6Xor53vvIlcbjUuIGtleURlY2lzaW9uczog5YWz6ZSu5Yaz562W5YiX6KGoXG42LiBhY3Rpb25JdGVtczog6KGM5Yqo6aG55YiX6KGo77yM5q+P5Liq5YyF5ZCrIHRhc2vvvIjku7vliqHvvInjgIFhc3NpZ25lZe+8iOi0n+i0o+S6uu+8ieOAgWRlYWRsaW5l77yI5oiq5q2i5pel5pyf77yM5Y+v6YCJ77yJ44CBcHJpb3JpdHnvvIjkvJjlhYjnuqfvvJpoaWdoL21lZGl1bS9sb3fvvIlcbjcuIG5leHRTdGVwczog5LiL5LiA5q2l6K6h5YiSXG5cbuS8muiuruWGheWuue+8mlxuJHttZWV0aW5nVGV4dH1cblxu6K+356Gu5L+d6L+U5Zue55qE5piv5pyJ5pWI55qESlNPTuagvOW8j++8jOS4jeimgeWMheWQq+S7u+S9leWFtuS7luaWh+acrOOAglxuYDtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vYXBpL2dlbmVyYXRlYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBtb2RlbDogdGhpcy5tb2RlbCxcbiAgICAgICAgICBwcm9tcHQ6IHByb21wdCxcbiAgICAgICAgICBzdHJlYW06IGZhbHNlLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgT2xsYW1hIEFQSSBlcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGE6IE9sbGFtYVJlc3BvbnNlID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgXG4gICAgICAvLyDlsJ3or5Xop6PmnpAgSlNPTiDlk43lupRcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGNsZWFuZWRSZXNwb25zZSA9IGRhdGEucmVzcG9uc2UudHJpbSgpO1xuICAgICAgICAvLyDnp7vpmaTlj6/og73nmoQgbWFya2Rvd24g5Luj56CB5Z2X5qCH6K6wXG4gICAgICAgIGNvbnN0IGpzb25NYXRjaCA9IGNsZWFuZWRSZXNwb25zZS5tYXRjaCgvYGBgKD86anNvbik/XFxzKihbXFxzXFxTXSo/KVxccypgYGAvKTtcbiAgICAgICAgY29uc3QganNvblN0cmluZyA9IGpzb25NYXRjaCA/IGpzb25NYXRjaFsxXSA6IGNsZWFuZWRSZXNwb25zZTtcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IG1lZXRpbmdNaW51dGVzOiBNZWV0aW5nTWludXRlcyA9IEpTT04ucGFyc2UoanNvblN0cmluZyk7XG4gICAgICAgIFxuICAgICAgICAvLyDpqozor4Hlv4XopoHlrZfmrrVcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy50aXRsZSkgbWVldGluZ01pbnV0ZXMudGl0bGUgPSAn5Lya6K6u57qq6KaBJztcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy5kYXRlKSBtZWV0aW5nTWludXRlcy5kYXRlID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMucGFydGljaXBhbnRzKSBtZWV0aW5nTWludXRlcy5wYXJ0aWNpcGFudHMgPSBbXTtcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy5zdW1tYXJ5KSBtZWV0aW5nTWludXRlcy5zdW1tYXJ5ID0gJ+S8muiuruaRmOimgeeUn+aIkOS4rS4uLic7XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMua2V5RGVjaXNpb25zKSBtZWV0aW5nTWludXRlcy5rZXlEZWNpc2lvbnMgPSBbXTtcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy5hY3Rpb25JdGVtcykgbWVldGluZ01pbnV0ZXMuYWN0aW9uSXRlbXMgPSBbXTtcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy5uZXh0U3RlcHMpIG1lZXRpbmdNaW51dGVzLm5leHRTdGVwcyA9IFtdO1xuICAgICAgICBcbiAgICAgICAgcmV0dXJuIG1lZXRpbmdNaW51dGVzO1xuICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdKU09OIHBhcnNpbmcgZXJyb3I6JywgcGFyc2VFcnJvcik7XG4gICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOi/lOWbnuWfuuacrOe7k+aehFxuICAgICAgICByZXR1cm4gdGhpcy5jcmVhdGVGYWxsYmFja01pbnV0ZXMobWVldGluZ1RleHQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdPbGxhbWEgc2VydmljZSBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ+aXoOazlei/nuaOpeWIsCBPbGxhbWEg5pyN5Yqh44CC6K+356Gu5L+dIE9sbGFtYSDmraPlnKjov5DooYzlubbkuJTmqKHlnovlt7LkuIvovb3jgIInKTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGNyZWF0ZUZhbGxiYWNrTWludXRlcyhtZWV0aW5nVGV4dDogc3RyaW5nKTogTWVldGluZ01pbnV0ZXMge1xuICAgIHJldHVybiB7XG4gICAgICB0aXRsZTogJ+S8muiurue6quimgScsXG4gICAgICBkYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgIHBhcnRpY2lwYW50czogW10sXG4gICAgICBzdW1tYXJ5OiAn55Sx5LqOQUnmnI3liqHmmoLml7bkuI3lj6/nlKjvvIzor7fmiYvliqjmlbTnkIbkvJrorq7nuqropoHjgIInLFxuICAgICAga2V5RGVjaXNpb25zOiBbJ+ivt+aJi+WKqOa3u+WKoOWFs+mUruWGs+etliddLFxuICAgICAgYWN0aW9uSXRlbXM6IFt7XG4gICAgICAgIHRhc2s6ICfor7fmiYvliqjmt7vliqDooYzliqjpobknLFxuICAgICAgICBhc3NpZ25lZTogJ+W+heWIhumFjScsXG4gICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyBhcyBjb25zdFxuICAgICAgfV0sXG4gICAgICBuZXh0U3RlcHM6IFsn6K+35omL5Yqo5re75Yqg5LiL5LiA5q2l6K6h5YiSJ11cbiAgICB9O1xuICB9XG5cbiAgYXN5bmMgY2hlY2tDb25uZWN0aW9uKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vYXBpL3RhZ3NgKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5vaztcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBsaXN0TW9kZWxzKCk6IFByb21pc2U8c3RyaW5nW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2FwaS90YWdzYCk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSByZXR1cm4gW107XG4gICAgICBcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gZGF0YS5tb2RlbHM/Lm1hcCgobW9kZWw6IGFueSkgPT4gbW9kZWwubmFtZSkgfHwgW107XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiT2xsYW1hU2VydmljZSIsImdlbmVyYXRlTWVldGluZ01pbnV0ZXMiLCJtZWV0aW5nVGV4dCIsInByb21wdCIsInJlc3BvbnNlIiwiZmV0Y2giLCJiYXNlVXJsIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibW9kZWwiLCJzdHJlYW0iLCJvayIsIkVycm9yIiwic3RhdHVzIiwiZGF0YSIsImpzb24iLCJjbGVhbmVkUmVzcG9uc2UiLCJ0cmltIiwianNvbk1hdGNoIiwibWF0Y2giLCJqc29uU3RyaW5nIiwibWVldGluZ01pbnV0ZXMiLCJwYXJzZSIsInRpdGxlIiwiZGF0ZSIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwicGFydGljaXBhbnRzIiwic3VtbWFyeSIsImtleURlY2lzaW9ucyIsImFjdGlvbkl0ZW1zIiwibmV4dFN0ZXBzIiwicGFyc2VFcnJvciIsImNvbnNvbGUiLCJlcnJvciIsImNyZWF0ZUZhbGxiYWNrTWludXRlcyIsInRhc2siLCJhc3NpZ25lZSIsInByaW9yaXR5IiwiY2hlY2tDb25uZWN0aW9uIiwibGlzdE1vZGVscyIsIm1vZGVscyIsIm1hcCIsIm5hbWUiLCJjb25zdHJ1Y3RvciIsImRlbW9Nb2RlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});