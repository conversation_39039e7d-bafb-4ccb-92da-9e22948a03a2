"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MeetingMinutesGenerator.tsx":
/*!****************************************************!*\
  !*** ./src/components/MeetingMinutesGenerator.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MeetingMinutesGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/ollama */ \"(app-pages-browser)/./src/lib/ollama.ts\");\n/* harmony import */ var _components_OllamaStatus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/OllamaStatus */ \"(app-pages-browser)/./src/components/OllamaStatus.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MeetingMinutesGenerator() {\n    _s();\n    const [meetingText, setMeetingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [meetingMinutes, setMeetingMinutes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [demoMode, setDemoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('qwq:32b');\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"MeetingMinutesGenerator.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_6__.OllamaService('http://localhost:11434', selectedModel, demoMode)\n    }[\"MeetingMinutesGenerator.useState\"]);\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file && file.type === 'text/plain') {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const content = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setMeetingText(content);\n            };\n            reader.readAsText(file);\n        } else {\n            setError('请上传 .txt 格式的文件');\n        }\n    };\n    const generateMinutes = async ()=>{\n        if (!meetingText.trim()) {\n            setError('请输入会议内容');\n            return;\n        }\n        setIsGenerating(true);\n        setError(null);\n        try {\n            // 创建新的服务实例以使用当前的演示模式设置\n            const service = new _lib_ollama__WEBPACK_IMPORTED_MODULE_6__.OllamaService('http://localhost:11434', 'qwq:32b', demoMode);\n            const minutes = await service.generateMeetingMinutes(meetingText);\n            setMeetingMinutes(minutes);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : '生成会议纪要时发生错误');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const downloadMinutes = ()=>{\n        if (!meetingMinutes) return;\n        const content = \"\\n# \".concat(meetingMinutes.title, \"\\n\\n**日期：** \").concat(meetingMinutes.date, \"\\n\\n**参与者：** \").concat(meetingMinutes.participants.join(', '), \"\\n\\n## 会议摘要\\n\").concat(meetingMinutes.summary, \"\\n\\n## 关键决策\\n\").concat(meetingMinutes.keyDecisions.map((decision)=>\"- \".concat(decision)).join('\\n'), \"\\n\\n## 行动项\\n\").concat(meetingMinutes.actionItems.map((item)=>\"- **\".concat(item.task, \"** (负责人: \").concat(item.assignee, \", 优先级: \").concat(item.priority).concat(item.deadline ? \", 截止日期: \".concat(item.deadline) : '', \")\")).join('\\n'), \"\\n\\n## 下一步计划\\n\").concat(meetingMinutes.nextSteps.map((step)=>\"- \".concat(step)).join('\\n'), \"\\n\");\n        const blob = new Blob([\n            content\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"\".concat(meetingMinutes.title.replace(/\\s+/g, '_'), \"_\").concat(meetingMinutes.date, \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'low':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"智能会议纪要生成器\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"使用本地 AI 技术自动生成结构化的会议纪要\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-2 mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center gap-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: demoMode,\n                                    onChange: (e)=>setDemoMode(e.target.checked),\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                \"演示模式（无需 Ollama）\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OllamaStatus__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                demoMode: demoMode\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"会议内容输入\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"粘贴会议文本或上传文本文件\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"上传文本文件\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"file-upload\",\n                                                type: \"file\",\n                                                accept: \".txt\",\n                                                onChange: handleFileUpload,\n                                                className: \"cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                placeholder: \"在此粘贴会议内容，包括讨论要点、决策和任务分配...\",\n                                                value: meetingText,\n                                                onChange: (e)=>setMeetingText(e.target.value),\n                                                className: \"min-h-[300px] resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 right-2 text-xs text-gray-400\",\n                                                children: [\n                                                    meetingText.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: generateMinutes,\n                                        disabled: isGenerating || !meetingText.trim(),\n                                        className: \"w-full\",\n                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"正在生成纪要...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"生成会议纪要\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"生成的会议纪要\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            meetingMinutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: downloadMinutes,\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"下载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"AI 生成的结构化会议纪要\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: meetingMinutes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: meetingMinutes.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"日期: \",\n                                                        meetingMinutes.date\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                meetingMinutes.participants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"参与者: \",\n                                                        meetingMinutes.participants.join(', ')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"会议摘要\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 bg-gray-50 p-3 rounded-md\",\n                                                    children: meetingMinutes.summary\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        meetingMinutes.keyDecisions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"关键决策\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.keyDecisions.map((decision, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                decision\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.actionItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"行动项\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: meetingMinutes.actionItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-md p-3 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: item.task\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(item.priority)),\n                                                                            children: item.priority\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1 text-xs text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"负责人: \",\n                                                                                item.assignee\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 252,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        item.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                \"截止: \",\n                                                                                item.deadline\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 47\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.nextSteps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"下一步计划\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.nextSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                step\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"在左侧输入会议内容后，点击生成按钮\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(MeetingMinutesGenerator, \"EoLUNxc8YZ5iRN0doZqNkzObaPE=\");\n_c = MeetingMinutesGenerator;\nvar _c;\n$RefreshReg$(_c, \"MeetingMinutesGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MeetingMinutesGenerator.tsx\n"));

/***/ })

});