'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { OllamaService } from '@/lib/ollama';
import { CheckCircle, XCircle, RefreshCw, Server } from 'lucide-react';

interface OllamaStatusProps {
  demoMode?: boolean;
}

export default function OllamaStatus({ demoMode = false }: OllamaStatusProps) {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [models, setModels] = useState<string[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [ollamaService] = useState(() => new OllamaService());

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      if (demoMode) {
        setIsConnected(true);
        setModels(['演示模式 (无需真实模型)']);
      } else {
        const connected = await ollamaService.checkConnection();
        setIsConnected(connected);

        if (connected) {
          const availableModels = await ollamaService.listModels();
          setModels(availableModels);
        } else {
          setModels([]);
        }
      }
    } catch (error) {
      setIsConnected(false);
      setModels([]);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
  }, []);

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          Ollama 服务状态
          <Button
            onClick={checkConnection}
            disabled={isChecking}
            variant="outline"
            size="sm"
            className="ml-auto"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            {isConnected === null ? (
              <div className="h-4 w-4 bg-gray-300 rounded-full animate-pulse" />
            ) : isConnected ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">
              {isConnected === null
                ? '检查连接中...'
                : isConnected
                ? 'Ollama 服务已连接'
                : 'Ollama 服务未连接'}
            </span>
          </div>

          {isConnected && models.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-2">可用模型:</p>
              <div className="flex flex-wrap gap-2">
                {models.map((model) => (
                  <span
                    key={model}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
                  >
                    {model}
                  </span>
                ))}
              </div>
            </div>
          )}

          {!isConnected && (
            <div className="text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <p className="font-medium mb-2">如何启动 Ollama:</p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>确保已安装 Ollama: <code className="bg-gray-100 px-1 rounded">curl -fsSL https://ollama.ai/install.sh | sh</code></li>
                <li>启动 Ollama 服务: <code className="bg-gray-100 px-1 rounded">ollama serve</code></li>
                <li>下载推荐模型: <code className="bg-gray-100 px-1 rounded">ollama pull llama3.1</code></li>
              </ol>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
