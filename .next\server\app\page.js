/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cpr%5Chtml%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpr%5Chtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cpr%5Chtml%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpr%5Chtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst page2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page2, \"D:\\\\pr\\\\html\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\pr\\\\html\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\pr\\\\html\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cpr%5Chtml%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpr%5Chtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Ccomponents%5C%5CMeetingMinutesGenerator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Ccomponents%5C%5CMeetingMinutesGenerator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/MeetingMinutesGenerator.tsx */ \"(rsc)/./src/components/MeetingMinutesGenerator.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwciU1QyU1Q2h0bWwlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDTWVldGluZ01pbnV0ZXNHZW5lcmF0b3IudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTRIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxccHJcXFxcaHRtbFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxNZWV0aW5nTWludXRlc0dlbmVyYXRvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Ccomponents%5C%5CMeetingMinutesGenerator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Ccomponents%5C%5CMeetingMinutesGenerator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Ccomponents%5C%5CMeetingMinutesGenerator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/MeetingMinutesGenerator.tsx */ \"(ssr)/./src/components/MeetingMinutesGenerator.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwciU1QyU1Q2h0bWwlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDTWVldGluZ01pbnV0ZXNHZW5lcmF0b3IudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb01BQTRIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxccHJcXFxcaHRtbFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxNZWV0aW5nTWludXRlc0dlbmVyYXRvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cpr%5C%5Chtml%5C%5Csrc%5C%5Ccomponents%5C%5CMeetingMinutesGenerator.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/MeetingMinutesGenerator.tsx":
/*!****************************************************!*\
  !*** ./src/components/MeetingMinutesGenerator.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MeetingMinutesGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/ollama */ \"(ssr)/./src/lib/ollama.ts\");\n/* harmony import */ var _components_OllamaStatus__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/OllamaStatus */ \"(ssr)/./src/components/OllamaStatus.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction MeetingMinutesGenerator() {\n    const [meetingText, setMeetingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [meetingMinutes, setMeetingMinutes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [demoMode, setDemoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('qwq:32b');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [progressValue, setProgressValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streamContent, setStreamContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"MeetingMinutesGenerator.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_7__.OllamaService('http://localhost:11434', selectedModel, demoMode)\n    }[\"MeetingMinutesGenerator.useState\"]);\n    const handleFileUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (file && file.type === 'text/plain') {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                const content = e.target?.result;\n                setMeetingText(content);\n            };\n            reader.readAsText(file);\n        } else {\n            setError('请上传 .txt 格式的文件');\n        }\n    };\n    const generateMinutes = async ()=>{\n        if (!meetingText.trim()) {\n            setError('请输入会议内容');\n            return;\n        }\n        setIsGenerating(true);\n        setError(null);\n        setCurrentStep('');\n        setGenerationProgress([]);\n        setProgressValue(0);\n        setStreamContent('');\n        setMeetingMinutes(null);\n        try {\n            // 创建新的服务实例以使用当前的演示模式和模型设置\n            const service = new _lib_ollama__WEBPACK_IMPORTED_MODULE_7__.OllamaService('http://localhost:11434', selectedModel, demoMode);\n            const minutes = await service.generateMeetingMinutes(meetingText, (step, content)=>{\n                setCurrentStep(step);\n                setStreamContent(content);\n                setGenerationProgress((prev)=>{\n                    if (!prev.includes(step)) {\n                        const newProgress = [\n                            ...prev,\n                            step\n                        ];\n                        setProgressValue(newProgress.length / 6 * 100); // 假设有6个步骤\n                        return newProgress;\n                    }\n                    return prev;\n                });\n            });\n            setCurrentStep('✅ 生成完成！');\n            setMeetingMinutes(minutes);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : '生成会议纪要时发生错误');\n            setCurrentStep('');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const downloadMinutes = ()=>{\n        if (!meetingMinutes) return;\n        const content = `\n# ${meetingMinutes.title}\n\n**日期：** ${meetingMinutes.date}\n\n**参与者：** ${meetingMinutes.participants.join(', ')}\n\n## 会议摘要\n${meetingMinutes.summary}\n\n## 关键决策\n${meetingMinutes.keyDecisions.map((decision)=>`- ${decision}`).join('\\n')}\n\n## 行动项\n${meetingMinutes.actionItems.map((item)=>`- **${item.task}** (负责人: ${item.assignee}, 优先级: ${item.priority}${item.deadline ? `, 截止日期: ${item.deadline}` : ''})`).join('\\n')}\n\n## 下一步计划\n${meetingMinutes.nextSteps.map((step)=>`- ${step}`).join('\\n')}\n`;\n        const blob = new Blob([\n            content\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${meetingMinutes.title.replace(/\\s+/g, '_')}_${meetingMinutes.date}.md`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'low':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"智能会议纪要生成器\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"使用本地 AI 技术自动生成结构化的会议纪要\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-6 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center gap-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: demoMode,\n                                        onChange: (e)=>setDemoMode(e.target.checked),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"演示模式（无需 Ollama）\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            !demoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"model-select\",\n                                        children: \"AI 模型:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"model-select\",\n                                        value: selectedModel,\n                                        onChange: (e)=>setSelectedModel(e.target.value),\n                                        className: \"px-2 py-1 border border-gray-300 rounded text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwq:32b\",\n                                                children: \"qwq:32b (推荐)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwen2.5:7b\",\n                                                children: \"qwen2.5:7b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwen2.5:0.5b\",\n                                                children: \"qwen2.5:0.5b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"llama3.1:8b\",\n                                                children: \"llama3.1:8b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"llama3.2:3b\",\n                                                children: \"llama3.2:3b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OllamaStatus__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                demoMode: demoMode\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"会议内容输入\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"粘贴会议文本或上传文本文件\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"上传文本文件\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"file-upload\",\n                                                type: \"file\",\n                                                accept: \".txt\",\n                                                onChange: handleFileUpload,\n                                                className: \"cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                placeholder: \"在此粘贴会议内容，包括讨论要点、决策和任务分配...\",\n                                                value: meetingText,\n                                                onChange: (e)=>setMeetingText(e.target.value),\n                                                className: \"min-h-[300px] resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 right-2 text-xs text-gray-400\",\n                                                children: [\n                                                    meetingText.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: generateMinutes,\n                                        disabled: isGenerating || !meetingText.trim(),\n                                        className: \"w-full\",\n                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"正在生成纪要...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"生成会议纪要\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-50 border border-blue-200 rounded-md space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-700 font-medium\",\n                                                        children: \"正在生成会议纪要\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                        value: progressValue,\n                                                        className: \"h-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600 text-right\",\n                                                        children: [\n                                                            Math.round(progressValue),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-600 font-medium\",\n                                                children: currentStep\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            streamContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-3 rounded border text-xs text-gray-600 max-h-20 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"AI 生成预览:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"whitespace-pre-wrap\",\n                                                        children: streamContent\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            generationProgress.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600 font-medium\",\n                                                        children: \"已完成步骤:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    generationProgress.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-xs text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                step\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"生成的会议纪要\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this),\n                                            meetingMinutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: downloadMinutes,\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"下载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"AI 生成的结构化会议纪要\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: meetingMinutes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: meetingMinutes.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"日期: \",\n                                                        meetingMinutes.date\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                meetingMinutes.participants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"参与者: \",\n                                                        meetingMinutes.participants.join(', ')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"会议摘要\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 bg-gray-50 p-3 rounded-md\",\n                                                    children: meetingMinutes.summary\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        meetingMinutes.keyDecisions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"关键决策\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.keyDecisions.map((decision, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                decision\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.actionItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"行动项\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: meetingMinutes.actionItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-md p-3 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: item.task\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`,\n                                                                            children: item.priority\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1 text-xs text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"负责人: \",\n                                                                                item.assignee\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        item.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                \"截止: \",\n                                                                                item.deadline\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 47\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.nextSteps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"下一步计划\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.nextSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                step\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"在左侧输入会议内容后，点击生成按钮\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9NZWV0aW5nTWludXRlc0dlbmVyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ1E7QUFDaUQ7QUFDN0M7QUFDTjtBQUNNO0FBQ2M7QUFDYjtBQUNzQztBQUU1RSxTQUFTa0I7SUFDdEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNvQixjQUFjQyxnQkFBZ0IsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3NCLGdCQUFnQkMsa0JBQWtCLEdBQUd2QiwrQ0FBUUEsQ0FBd0I7SUFDNUUsTUFBTSxDQUFDd0IsT0FBT0MsU0FBUyxHQUFHekIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQzBCLFVBQVVDLFlBQVksR0FBRzNCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzRCLGVBQWVDLGlCQUFpQixHQUFHN0IsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDOEIsYUFBYUMsZUFBZSxHQUFHL0IsK0NBQVFBLENBQVM7SUFDdkQsTUFBTSxDQUFDZ0Msb0JBQW9CQyxzQkFBc0IsR0FBR2pDLCtDQUFRQSxDQUFXLEVBQUU7SUFDekUsTUFBTSxDQUFDa0MsZUFBZUMsaUJBQWlCLEdBQUduQywrQ0FBUUEsQ0FBUztJQUMzRCxNQUFNLENBQUNvQyxlQUFlQyxpQkFBaUIsR0FBR3JDLCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQ3NDLGNBQWMsR0FBR3RDLCtDQUFRQTs0Q0FBQyxJQUFNLElBQUlVLHNEQUFhQSxDQUFDLDBCQUEwQmtCLGVBQWVGOztJQUVsRyxNQUFNYSxtQkFBbUIsQ0FBQ0M7UUFDeEIsTUFBTUMsT0FBT0QsTUFBTUUsTUFBTSxDQUFDQyxLQUFLLEVBQUUsQ0FBQyxFQUFFO1FBQ3BDLElBQUlGLFFBQVFBLEtBQUtHLElBQUksS0FBSyxjQUFjO1lBQ3RDLE1BQU1DLFNBQVMsSUFBSUM7WUFDbkJELE9BQU9FLE1BQU0sR0FBRyxDQUFDQztnQkFDZixNQUFNQyxVQUFVRCxFQUFFTixNQUFNLEVBQUVRO2dCQUMxQi9CLGVBQWU4QjtZQUNqQjtZQUNBSixPQUFPTSxVQUFVLENBQUNWO1FBQ3BCLE9BQU87WUFDTGhCLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTTJCLGtCQUFrQjtRQUN0QixJQUFJLENBQUNsQyxZQUFZbUMsSUFBSSxJQUFJO1lBQ3ZCNUIsU0FBUztZQUNUO1FBQ0Y7UUFFQUosZ0JBQWdCO1FBQ2hCSSxTQUFTO1FBQ1RNLGVBQWU7UUFDZkUsc0JBQXNCLEVBQUU7UUFDeEJFLGlCQUFpQjtRQUNqQkUsaUJBQWlCO1FBQ2pCZCxrQkFBa0I7UUFFbEIsSUFBSTtZQUNGLDBCQUEwQjtZQUMxQixNQUFNK0IsVUFBVSxJQUFJNUMsc0RBQWFBLENBQUMsMEJBQTBCa0IsZUFBZUY7WUFFM0UsTUFBTTZCLFVBQVUsTUFBTUQsUUFBUUUsc0JBQXNCLENBQ2xEdEMsYUFDQSxDQUFDdUMsTUFBY1I7Z0JBQ2JsQixlQUFlMEI7Z0JBQ2ZwQixpQkFBaUJZO2dCQUNqQmhCLHNCQUFzQnlCLENBQUFBO29CQUNwQixJQUFJLENBQUNBLEtBQUtDLFFBQVEsQ0FBQ0YsT0FBTzt3QkFDeEIsTUFBTUcsY0FBYzsrQkFBSUY7NEJBQU1EO3lCQUFLO3dCQUNuQ3RCLGlCQUFpQixZQUFhMEIsTUFBTSxHQUFHLElBQUssTUFBTSxVQUFVO3dCQUM1RCxPQUFPRDtvQkFDVDtvQkFDQSxPQUFPRjtnQkFDVDtZQUNGO1lBR0YzQixlQUFlO1lBQ2ZSLGtCQUFrQmdDO1FBQ3BCLEVBQUUsT0FBT08sS0FBSztZQUNackMsU0FBU3FDLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztZQUM5Q2pDLGVBQWU7UUFDakIsU0FBVTtZQUNSVixnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU00QyxrQkFBa0I7UUFDdEIsSUFBSSxDQUFDM0MsZ0JBQWdCO1FBRXJCLE1BQU0yQixVQUFVLENBQUM7RUFDbkIsRUFBRTNCLGVBQWU0QyxLQUFLLENBQUM7O1FBRWpCLEVBQUU1QyxlQUFlNkMsSUFBSSxDQUFDOztTQUVyQixFQUFFN0MsZUFBZThDLFlBQVksQ0FBQ0MsSUFBSSxDQUFDLE1BQU07OztBQUdsRCxFQUFFL0MsZUFBZWdELE9BQU8sQ0FBQzs7O0FBR3pCLEVBQUVoRCxlQUFlaUQsWUFBWSxDQUFDQyxHQUFHLENBQUNDLENBQUFBLFdBQVksQ0FBQyxFQUFFLEVBQUVBLFVBQVUsRUFBRUosSUFBSSxDQUFDLE1BQU07OztBQUcxRSxFQUFFL0MsZUFBZW9ELFdBQVcsQ0FBQ0YsR0FBRyxDQUFDRyxDQUFBQSxPQUMvQixDQUFDLElBQUksRUFBRUEsS0FBS0MsSUFBSSxDQUFDLFNBQVMsRUFBRUQsS0FBS0UsUUFBUSxDQUFDLE9BQU8sRUFBRUYsS0FBS0csUUFBUSxHQUFHSCxLQUFLSSxRQUFRLEdBQUcsQ0FBQyxRQUFRLEVBQUVKLEtBQUtJLFFBQVEsRUFBRSxHQUFHLEdBQUcsQ0FBQyxDQUFDLEVBQ3JIVixJQUFJLENBQUMsTUFBTTs7O0FBR2IsRUFBRS9DLGVBQWUwRCxTQUFTLENBQUNSLEdBQUcsQ0FBQ2YsQ0FBQUEsT0FBUSxDQUFDLEVBQUUsRUFBRUEsTUFBTSxFQUFFWSxJQUFJLENBQUMsTUFBTTtBQUMvRCxDQUFDO1FBRUcsTUFBTVksT0FBTyxJQUFJQyxLQUFLO1lBQUNqQztTQUFRLEVBQUU7WUFBRUwsTUFBTTtRQUFnQjtRQUN6RCxNQUFNdUMsTUFBTUMsSUFBSUMsZUFBZSxDQUFDSjtRQUNoQyxNQUFNSyxJQUFJQyxTQUFTQyxhQUFhLENBQUM7UUFDakNGLEVBQUVHLElBQUksR0FBR047UUFDVEcsRUFBRUksUUFBUSxHQUFHLEdBQUdwRSxlQUFlNEMsS0FBSyxDQUFDeUIsT0FBTyxDQUFDLFFBQVEsS0FBSyxDQUFDLEVBQUVyRSxlQUFlNkMsSUFBSSxDQUFDLEdBQUcsQ0FBQztRQUNyRm9CLFNBQVNLLElBQUksQ0FBQ0MsV0FBVyxDQUFDUDtRQUMxQkEsRUFBRVEsS0FBSztRQUNQUCxTQUFTSyxJQUFJLENBQUNHLFdBQVcsQ0FBQ1Q7UUFDMUJGLElBQUlZLGVBQWUsQ0FBQ2I7SUFDdEI7SUFFQSxNQUFNYyxtQkFBbUIsQ0FBQ25CO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFPLE9BQU87WUFDbkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNvQjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBbUM7Ozs7OztrQ0FDakQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFnQjs7Ozs7O2tDQUM3Qiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRztnQ0FBTUgsV0FBVTs7a0RBQ2YsOERBQUNJO3dDQUNDM0QsTUFBSzt3Q0FDTDRELFNBQVM5RTt3Q0FDVCtFLFVBQVUsQ0FBQ3pELElBQU1yQixZQUFZcUIsRUFBRU4sTUFBTSxDQUFDOEQsT0FBTzt3Q0FDN0NMLFdBQVU7Ozs7OztvQ0FDVjs7Ozs7Ozs0QkFJSCxDQUFDekUsMEJBQ0EsOERBQUN3RTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFNSSxTQUFRO2tEQUFlOzs7Ozs7a0RBQzlCLDhEQUFDQzt3Q0FDQ0MsSUFBRzt3Q0FDSEMsT0FBT2pGO3dDQUNQNkUsVUFBVSxDQUFDekQsSUFBTW5CLGlCQUFpQm1CLEVBQUVOLE1BQU0sQ0FBQ21FLEtBQUs7d0NBQ2hEVixXQUFVOzswREFFViw4REFBQ1c7Z0RBQU9ELE9BQU07MERBQVU7Ozs7OzswREFDeEIsOERBQUNDO2dEQUFPRCxPQUFNOzBEQUFhOzs7Ozs7MERBQzNCLDhEQUFDQztnREFBT0QsT0FBTTswREFBZTs7Ozs7OzBEQUM3Qiw4REFBQ0M7Z0RBQU9ELE9BQU07MERBQWM7Ozs7OzswREFDNUIsOERBQUNDO2dEQUFPRCxPQUFNOzBEQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3RDLDhEQUFDbEcsZ0VBQVlBO2dCQUFDZSxVQUFVQTs7Ozs7OzBCQUV4Qiw4REFBQ3dFO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ2pHLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTs7a0RBQ1QsOERBQUNDLDBEQUFTQTt3Q0FBQzZGLFdBQVU7OzBEQUNuQiw4REFBQ3ZGLDJIQUFRQTtnREFBQ3VGLFdBQVU7Ozs7Ozs0Q0FBWTs7Ozs7OztrREFHbEMsOERBQUMvRixnRUFBZUE7a0RBQUM7Ozs7Ozs7Ozs7OzswQ0FJbkIsOERBQUNELDREQUFXQTtnQ0FBQ2dHLFdBQVU7O2tEQUNyQiw4REFBQ0Q7OzBEQUNDLDhEQUFDSTtnREFBTUksU0FBUTtnREFBY1AsV0FBVTswREFBaUM7Ozs7OzswREFHeEUsOERBQUMzRix1REFBS0E7Z0RBQ0pvRyxJQUFHO2dEQUNIaEUsTUFBSztnREFDTG1FLFFBQU87Z0RBQ1BOLFVBQVVsRTtnREFDVjRELFdBQVU7Ozs7Ozs7Ozs7OztrREFJZCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDNUYsNkRBQVFBO2dEQUNQeUcsYUFBWTtnREFDWkgsT0FBTzNGO2dEQUNQdUYsVUFBVSxDQUFDekQsSUFBTTdCLGVBQWU2QixFQUFFTixNQUFNLENBQUNtRSxLQUFLO2dEQUM5Q1YsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDRDtnREFBSUMsV0FBVTs7b0RBQ1pqRixZQUFZMkMsTUFBTTtvREFBQzs7Ozs7Ozs7Ozs7OztrREFJeEIsOERBQUM1RCx5REFBTUE7d0NBQ0xnSCxTQUFTN0Q7d0NBQ1Q4RCxVQUFVOUYsZ0JBQWdCLENBQUNGLFlBQVltQyxJQUFJO3dDQUMzQzhDLFdBQVU7a0RBRVQvRSw2QkFDQzs7OERBQ0UsOERBQUNQLDRIQUFLQTtvREFBQ3NGLFdBQVU7Ozs7OztnREFBOEI7O3lFQUlqRDs7OERBQ0UsOERBQUN0Riw0SEFBS0E7b0RBQUNzRixXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7OztvQ0FPdkMvRSw4QkFDQyw4REFBQzhFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDdEYsNEhBQUtBO3dEQUFDc0YsV0FBVTs7Ozs7O2tFQUNqQiw4REFBQ2dCO3dEQUFLaEIsV0FBVTtrRUFBNEI7Ozs7Ozs7Ozs7OzswREFJOUMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzFGLDZEQUFRQTt3REFBQ29HLE9BQU8zRTt3REFBZWlFLFdBQVU7Ozs7OztrRUFDMUMsOERBQUNEO3dEQUFJQyxXQUFVOzs0REFDWmlCLEtBQUtDLEtBQUssQ0FBQ25GOzREQUFlOzs7Ozs7Ozs7Ozs7OzRDQUs5QkosNkJBQ0MsOERBQUNvRTtnREFBSUMsV0FBVTswREFDWnJFOzs7Ozs7NENBS0pNLCtCQUNDLDhEQUFDOEQ7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBbUI7Ozs7OztrRUFDbEMsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUF1Qi9EOzs7Ozs7Ozs7Ozs7NENBS3pDSixtQkFBbUI2QixNQUFNLEdBQUcsbUJBQzNCLDhEQUFDcUM7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBb0M7Ozs7OztvREFDbERuRSxtQkFBbUJ3QyxHQUFHLENBQUMsQ0FBQ2YsTUFBTTZELHNCQUM3Qiw4REFBQ3BCOzREQUFnQkMsV0FBVTs7OEVBQ3pCLDhEQUFDcEYsNEhBQVdBO29FQUFDb0YsV0FBVTs7Ozs7O2dFQUN0QjFDOzsyREFGTzZEOzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FVbkI5Rix1QkFDQyw4REFBQzBFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ25GLDRIQUFXQTtnREFBQ21GLFdBQVU7Ozs7OzswREFDdkIsOERBQUNnQjtnREFBS2hCLFdBQVU7MERBQXdCM0U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPaEQsOERBQUN0QixxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7O2tEQUNULDhEQUFDQywwREFBU0E7d0NBQUM2RixXQUFVOzswREFDbkIsOERBQUNnQjtnREFBS2hCLFdBQVU7O2tFQUNkLDhEQUFDcEYsNEhBQVdBO3dEQUFDb0YsV0FBVTs7Ozs7O29EQUFZOzs7Ozs7OzRDQUdwQzdFLGdDQUNDLDhEQUFDckIseURBQU1BO2dEQUFDZ0gsU0FBU2hEO2dEQUFpQnNELFNBQVE7Z0RBQVVDLE1BQUs7O2tFQUN2RCw4REFBQzFHLDRIQUFRQTt3REFBQ3FGLFdBQVU7Ozs7OztvREFBaUI7Ozs7Ozs7Ozs7Ozs7a0RBSzNDLDhEQUFDL0YsZ0VBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBSW5CLDhEQUFDRCw0REFBV0E7MENBQ1RtQiwrQkFDQyw4REFBQzRFO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDdUI7b0RBQUd0QixXQUFVOzhEQUE4QjdFLGVBQWU0QyxLQUFLOzs7Ozs7OERBQ2hFLDhEQUFDbUM7b0RBQUVGLFdBQVU7O3dEQUF3Qjt3REFBSzdFLGVBQWU2QyxJQUFJOzs7Ozs7O2dEQUM1RDdDLGVBQWU4QyxZQUFZLENBQUNQLE1BQU0sR0FBRyxtQkFDcEMsOERBQUN3QztvREFBRUYsV0FBVTs7d0RBQXdCO3dEQUM3QjdFLGVBQWU4QyxZQUFZLENBQUNDLElBQUksQ0FBQzs7Ozs7Ozs7Ozs7OztzREFLN0MsOERBQUM2Qjs7OERBQ0MsOERBQUN3QjtvREFBR3ZCLFdBQVU7OERBQW1COzs7Ozs7OERBQ2pDLDhEQUFDRTtvREFBRUYsV0FBVTs4REFDVjdFLGVBQWVnRCxPQUFPOzs7Ozs7Ozs7Ozs7d0NBSTFCaEQsZUFBZWlELFlBQVksQ0FBQ1YsTUFBTSxHQUFHLG1CQUNwQyw4REFBQ3FDOzs4REFDQyw4REFBQ3dCO29EQUFHdkIsV0FBVTs4REFBbUI7Ozs7Ozs4REFDakMsOERBQUN3QjtvREFBR3hCLFdBQVU7OERBQ1g3RSxlQUFlaUQsWUFBWSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsVUFBVTZDLHNCQUMxQyw4REFBQ007NERBQWV6QixXQUFVOzs4RUFDeEIsOERBQUNnQjtvRUFBS2hCLFdBQVU7OEVBQXFCOzs7Ozs7Z0VBQ3BDMUI7OzJEQUZNNkM7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBU2hCaEcsZUFBZW9ELFdBQVcsQ0FBQ2IsTUFBTSxHQUFHLG1CQUNuQyw4REFBQ3FDOzs4REFDQyw4REFBQ3dCO29EQUFHdkIsV0FBVTs4REFBbUI7Ozs7Ozs4REFDakMsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNaN0UsZUFBZW9ELFdBQVcsQ0FBQ0YsR0FBRyxDQUFDLENBQUNHLE1BQU0yQyxzQkFDckMsOERBQUNwQjs0REFBZ0JDLFdBQVU7OzhFQUN6Qiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRTs0RUFBRUYsV0FBVTtzRkFBcUN4QixLQUFLQyxJQUFJOzs7Ozs7c0ZBQzNELDhEQUFDdUM7NEVBQUtoQixXQUFXLENBQUMsMkNBQTJDLEVBQUVGLGlCQUFpQnRCLEtBQUtHLFFBQVEsR0FBRztzRkFDN0ZILEtBQUtHLFFBQVE7Ozs7Ozs7Ozs7Ozs4RUFHbEIsOERBQUNvQjtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNnQjs7Z0ZBQUs7Z0ZBQU14QyxLQUFLRSxRQUFROzs7Ozs7O3dFQUN4QkYsS0FBS0ksUUFBUSxrQkFBSSw4REFBQ29DOzRFQUFLaEIsV0FBVTs7Z0ZBQU87Z0ZBQUt4QixLQUFLSSxRQUFROzs7Ozs7Ozs7Ozs7OzsyREFUckR1Qzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FpQmpCaEcsZUFBZTBELFNBQVMsQ0FBQ25CLE1BQU0sR0FBRyxtQkFDakMsOERBQUNxQzs7OERBQ0MsOERBQUN3QjtvREFBR3ZCLFdBQVU7OERBQW1COzs7Ozs7OERBQ2pDLDhEQUFDd0I7b0RBQUd4QixXQUFVOzhEQUNYN0UsZUFBZTBELFNBQVMsQ0FBQ1IsR0FBRyxDQUFDLENBQUNmLE1BQU02RCxzQkFDbkMsOERBQUNNOzREQUFlekIsV0FBVTs7OEVBQ3hCLDhEQUFDZ0I7b0VBQUtoQixXQUFVOzhFQUFzQjs7Ozs7O2dFQUNyQzFDOzsyREFGTTZEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7eURBVW5CLDhEQUFDcEI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDdkYsMkhBQVFBOzRDQUFDdUYsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ0U7c0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUW5CIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcY29tcG9uZW50c1xcTWVldGluZ01pbnV0ZXNHZW5lcmF0b3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCc7XG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90ZXh0YXJlYSc7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCc7XG5pbXBvcnQgeyBQcm9ncmVzcyB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9wcm9ncmVzcyc7XG5pbXBvcnQgeyBPbGxhbWFTZXJ2aWNlLCB0eXBlIE1lZXRpbmdNaW51dGVzIH0gZnJvbSAnQC9saWIvb2xsYW1hJztcbmltcG9ydCBPbGxhbWFTdGF0dXMgZnJvbSAnQC9jb21wb25lbnRzL09sbGFtYVN0YXR1cyc7XG5pbXBvcnQgeyBGaWxlVGV4dCwgVXBsb2FkLCBXYW5kMiwgRG93bmxvYWQsIENoZWNrQ2lyY2xlLCBBbGVydENpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1lZXRpbmdNaW51dGVzR2VuZXJhdG9yKCkge1xuICBjb25zdCBbbWVldGluZ1RleHQsIHNldE1lZXRpbmdUZXh0XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzR2VuZXJhdGluZywgc2V0SXNHZW5lcmF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW21lZXRpbmdNaW51dGVzLCBzZXRNZWV0aW5nTWludXRlc10gPSB1c2VTdGF0ZTxNZWV0aW5nTWludXRlcyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZGVtb01vZGUsIHNldERlbW9Nb2RlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkTW9kZWwsIHNldFNlbGVjdGVkTW9kZWxdID0gdXNlU3RhdGUoJ3F3cTozMmInKTtcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2dlbmVyYXRpb25Qcm9ncmVzcywgc2V0R2VuZXJhdGlvblByb2dyZXNzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XG4gIGNvbnN0IFtwcm9ncmVzc1ZhbHVlLCBzZXRQcm9ncmVzc1ZhbHVlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XG4gIGNvbnN0IFtzdHJlYW1Db250ZW50LCBzZXRTdHJlYW1Db250ZW50XSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbb2xsYW1hU2VydmljZV0gPSB1c2VTdGF0ZSgoKSA9PiBuZXcgT2xsYW1hU2VydmljZSgnaHR0cDovL2xvY2FsaG9zdDoxMTQzNCcsIHNlbGVjdGVkTW9kZWwsIGRlbW9Nb2RlKSk7XG5cbiAgY29uc3QgaGFuZGxlRmlsZVVwbG9hZCA9IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgaWYgKGZpbGUgJiYgZmlsZS50eXBlID09PSAndGV4dC9wbGFpbicpIHtcbiAgICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7XG4gICAgICByZWFkZXIub25sb2FkID0gKGUpID0+IHtcbiAgICAgICAgY29uc3QgY29udGVudCA9IGUudGFyZ2V0Py5yZXN1bHQgYXMgc3RyaW5nO1xuICAgICAgICBzZXRNZWV0aW5nVGV4dChjb250ZW50KTtcbiAgICAgIH07XG4gICAgICByZWFkZXIucmVhZEFzVGV4dChmaWxlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0RXJyb3IoJ+ivt+S4iuS8oCAudHh0IOagvOW8j+eahOaWh+S7ticpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZW5lcmF0ZU1pbnV0ZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFtZWV0aW5nVGV4dC50cmltKCkpIHtcbiAgICAgIHNldEVycm9yKCfor7fovpPlhaXkvJrorq7lhoXlrrknKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc0dlbmVyYXRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgc2V0Q3VycmVudFN0ZXAoJycpO1xuICAgIHNldEdlbmVyYXRpb25Qcm9ncmVzcyhbXSk7XG4gICAgc2V0UHJvZ3Jlc3NWYWx1ZSgwKTtcbiAgICBzZXRTdHJlYW1Db250ZW50KCcnKTtcbiAgICBzZXRNZWV0aW5nTWludXRlcyhudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyDliJvlu7rmlrDnmoTmnI3liqHlrp7kvovku6Xkvb/nlKjlvZPliY3nmoTmvJTnpLrmqKHlvI/lkozmqKHlnovorr7nva5cbiAgICAgIGNvbnN0IHNlcnZpY2UgPSBuZXcgT2xsYW1hU2VydmljZSgnaHR0cDovL2xvY2FsaG9zdDoxMTQzNCcsIHNlbGVjdGVkTW9kZWwsIGRlbW9Nb2RlKTtcblxuICAgICAgY29uc3QgbWludXRlcyA9IGF3YWl0IHNlcnZpY2UuZ2VuZXJhdGVNZWV0aW5nTWludXRlcyhcbiAgICAgICAgbWVldGluZ1RleHQsXG4gICAgICAgIChzdGVwOiBzdHJpbmcsIGNvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNldEN1cnJlbnRTdGVwKHN0ZXApO1xuICAgICAgICAgIHNldFN0cmVhbUNvbnRlbnQoY29udGVudCk7XG4gICAgICAgICAgc2V0R2VuZXJhdGlvblByb2dyZXNzKHByZXYgPT4ge1xuICAgICAgICAgICAgaWYgKCFwcmV2LmluY2x1ZGVzKHN0ZXApKSB7XG4gICAgICAgICAgICAgIGNvbnN0IG5ld1Byb2dyZXNzID0gWy4uLnByZXYsIHN0ZXBdO1xuICAgICAgICAgICAgICBzZXRQcm9ncmVzc1ZhbHVlKChuZXdQcm9ncmVzcy5sZW5ndGggLyA2KSAqIDEwMCk7IC8vIOWBh+iuvuaciTbkuKrmraXpqqRcbiAgICAgICAgICAgICAgcmV0dXJuIG5ld1Byb2dyZXNzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHByZXY7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIHNldEN1cnJlbnRTdGVwKCfinIUg55Sf5oiQ5a6M5oiQ77yBJyk7XG4gICAgICBzZXRNZWV0aW5nTWludXRlcyhtaW51dGVzKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAn55Sf5oiQ5Lya6K6u57qq6KaB5pe25Y+R55Sf6ZSZ6K+vJyk7XG4gICAgICBzZXRDdXJyZW50U3RlcCgnJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzR2VuZXJhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGRvd25sb2FkTWludXRlcyA9ICgpID0+IHtcbiAgICBpZiAoIW1lZXRpbmdNaW51dGVzKSByZXR1cm47XG5cbiAgICBjb25zdCBjb250ZW50ID0gYFxuIyAke21lZXRpbmdNaW51dGVzLnRpdGxlfVxuXG4qKuaXpeacn++8mioqICR7bWVldGluZ01pbnV0ZXMuZGF0ZX1cblxuKirlj4LkuI7ogIXvvJoqKiAke21lZXRpbmdNaW51dGVzLnBhcnRpY2lwYW50cy5qb2luKCcsICcpfVxuXG4jIyDkvJrorq7mkZjopoFcbiR7bWVldGluZ01pbnV0ZXMuc3VtbWFyeX1cblxuIyMg5YWz6ZSu5Yaz562WXG4ke21lZXRpbmdNaW51dGVzLmtleURlY2lzaW9ucy5tYXAoZGVjaXNpb24gPT4gYC0gJHtkZWNpc2lvbn1gKS5qb2luKCdcXG4nKX1cblxuIyMg6KGM5Yqo6aG5XG4ke21lZXRpbmdNaW51dGVzLmFjdGlvbkl0ZW1zLm1hcChpdGVtID0+IFxuICBgLSAqKiR7aXRlbS50YXNrfSoqICjotJ/otKPkuro6ICR7aXRlbS5hc3NpZ25lZX0sIOS8mOWFiOe6pzogJHtpdGVtLnByaW9yaXR5fSR7aXRlbS5kZWFkbGluZSA/IGAsIOaIquatouaXpeacnzogJHtpdGVtLmRlYWRsaW5lfWAgOiAnJ30pYFxuKS5qb2luKCdcXG4nKX1cblxuIyMg5LiL5LiA5q2l6K6h5YiSXG4ke21lZXRpbmdNaW51dGVzLm5leHRTdGVwcy5tYXAoc3RlcCA9PiBgLSAke3N0ZXB9YCkuam9pbignXFxuJyl9XG5gO1xuXG4gICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtjb250ZW50XSwgeyB0eXBlOiAndGV4dC9tYXJrZG93bicgfSk7XG4gICAgY29uc3QgdXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTtcbiAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgIGEuaHJlZiA9IHVybDtcbiAgICBhLmRvd25sb2FkID0gYCR7bWVldGluZ01pbnV0ZXMudGl0bGUucmVwbGFjZSgvXFxzKy9nLCAnXycpfV8ke21lZXRpbmdNaW51dGVzLmRhdGV9Lm1kYDtcbiAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpO1xuICAgIGEuY2xpY2soKTtcbiAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGEpO1xuICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgfTtcblxuICBjb25zdCBnZXRQcmlvcml0eUNvbG9yID0gKHByaW9yaXR5OiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHByaW9yaXR5KSB7XG4gICAgICBjYXNlICdoaWdoJzogcmV0dXJuICd0ZXh0LXJlZC02MDAgYmctcmVkLTUwJztcbiAgICAgIGNhc2UgJ21lZGl1bSc6IHJldHVybiAndGV4dC15ZWxsb3ctNjAwIGJnLXllbGxvdy01MCc7XG4gICAgICBjYXNlICdsb3cnOiByZXR1cm4gJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTUwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAndGV4dC1ncmF5LTYwMCBiZy1ncmF5LTUwJztcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvIHAtNiBzcGFjZS15LTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS0yXCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPuaZuuiDveS8muiurue6quimgeeUn+aIkOWZqDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7kvb/nlKjmnKzlnLAgQUkg5oqA5pyv6Ieq5Yqo55Sf5oiQ57uT5p6E5YyW55qE5Lya6K6u57qq6KaBPC9wPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC02IG10LTRcIj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgIGNoZWNrZWQ9e2RlbW9Nb2RlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldERlbW9Nb2RlKGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICDmvJTnpLrmqKHlvI/vvIjml6DpnIAgT2xsYW1h77yJXG4gICAgICAgICAgPC9sYWJlbD5cblxuICAgICAgICAgIHshZGVtb01vZGUgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwibW9kZWwtc2VsZWN0XCI+QUkg5qih5Z6LOjwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICBpZD1cIm1vZGVsLXNlbGVjdFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkTW9kZWx9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZE1vZGVsKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0yIHB5LTEgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIHRleHQtc21cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInF3cTozMmJcIj5xd3E6MzJiICjmjqjojZApPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInF3ZW4yLjU6N2JcIj5xd2VuMi41OjdiPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInF3ZW4yLjU6MC41YlwiPnF3ZW4yLjU6MC41Yjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJsbGFtYTMuMTo4YlwiPmxsYW1hMy4xOjhiPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImxsYW1hMy4yOjNiXCI+bGxhbWEzLjI6M2I8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8T2xsYW1hU3RhdHVzIGRlbW9Nb2RlPXtkZW1vTW9kZX0gLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgIHsvKiDovpPlhaXljLrln58gKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgIOS8muiuruWGheWuuei+k+WFpVxuICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICDnspjotLTkvJrorq7mlofmnKzmiJbkuIrkvKDmlofmnKzmlofku7ZcbiAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImZpbGUtdXBsb2FkXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+XG4gICAgICAgICAgICAgICAg5LiK5Lyg5paH5pys5paH5Lu2XG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIGlkPVwiZmlsZS11cGxvYWRcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICBhY2NlcHQ9XCIudHh0XCJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlRmlsZVVwbG9hZH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuWcqOatpOeymOi0tOS8muiuruWGheWuue+8jOWMheaLrOiuqOiuuuimgeeCueOAgeWGs+etluWSjOS7u+WKoeWIhumFjS4uLlwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e21lZXRpbmdUZXh0fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TWVldGluZ1RleHQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1pbi1oLVszMDBweF0gcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0yIHJpZ2h0LTIgdGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAge21lZXRpbmdUZXh0Lmxlbmd0aH0g5a2X56ymXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2dlbmVyYXRlTWludXRlc31cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzR2VuZXJhdGluZyB8fCAhbWVldGluZ1RleHQudHJpbSgpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNHZW5lcmF0aW5nID8gKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8V2FuZDIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICDmraPlnKjnlJ/miJDnuqropoEuLi5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPFdhbmQyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICDnlJ/miJDkvJrorq7nuqropoFcbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICB7Lyog6L+b5bqm5pi+56S6ICovfVxuICAgICAgICAgICAge2lzR2VuZXJhdGluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLW1kIHNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxXYW5kMiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS01MDAgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS03MDAgZm9udC1tZWRpdW1cIj7mraPlnKjnlJ/miJDkvJrorq7nuqropoE8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Lyog6L+b5bqm5p2hICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8UHJvZ3Jlc3MgdmFsdWU9e3Byb2dyZXNzVmFsdWV9IGNsYXNzTmFtZT1cImgtMlwiIC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCB0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgIHtNYXRoLnJvdW5kKHByb2dyZXNzVmFsdWUpfSVcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIOW9k+WJjeatpemqpCAqL31cbiAgICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRTdGVwfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHsvKiDmtYHlvI/lhoXlrrnpooTop4ggKi99XG4gICAgICAgICAgICAgICAge3N0cmVhbUNvbnRlbnQgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTMgcm91bmRlZCBib3JkZXIgdGV4dC14cyB0ZXh0LWdyYXktNjAwIG1heC1oLTIwIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTFcIj5BSSDnlJ/miJDpooTop4g6PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2hpdGVzcGFjZS1wcmUtd3JhcFwiPntzdHJlYW1Db250ZW50fTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHsvKiDlt7LlrozmiJDmraXpqqQgKi99XG4gICAgICAgICAgICAgICAge2dlbmVyYXRpb25Qcm9ncmVzcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+5bey5a6M5oiQ5q2l6aqkOjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7Z2VuZXJhdGlvblByb2dyZXNzLm1hcCgoc3RlcCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQteHMgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzdGVwfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBwLTMgYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDAgdGV4dC1zbVwiPntlcnJvcn08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIOe7k+aenOaYvuekuuWMuuWfnyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICDnlJ/miJDnmoTkvJrorq7nuqropoFcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICB7bWVldGluZ01pbnV0ZXMgJiYgKFxuICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17ZG93bmxvYWRNaW51dGVzfSB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICDkuIvovb1cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgQUkg55Sf5oiQ55qE57uT5p6E5YyW5Lya6K6u57qq6KaBXG4gICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAge21lZXRpbmdNaW51dGVzID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTJcIj57bWVldGluZ01pbnV0ZXMudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPuaXpeacnzoge21lZXRpbmdNaW51dGVzLmRhdGV9PC9wPlxuICAgICAgICAgICAgICAgICAge21lZXRpbmdNaW51dGVzLnBhcnRpY2lwYW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAg5Y+C5LiO6ICFOiB7bWVldGluZ01pbnV0ZXMucGFydGljaXBhbnRzLmpvaW4oJywgJyl9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTJcIj7kvJrorq7mkZjopoE8L2g0PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwIGJnLWdyYXktNTAgcC0zIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICAgICAge21lZXRpbmdNaW51dGVzLnN1bW1hcnl9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7bWVldGluZ01pbnV0ZXMua2V5RGVjaXNpb25zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTJcIj7lhbPplK7lhrPnrZY8L2g0PlxuICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge21lZXRpbmdNaW51dGVzLmtleURlY2lzaW9ucy5tYXAoKGRlY2lzaW9uLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS01MDAgbXQtMVwiPuKAojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2RlY2lzaW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7bWVldGluZ01pbnV0ZXMuYWN0aW9uSXRlbXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMlwiPuihjOWKqOmhuTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge21lZXRpbmdNaW51dGVzLmFjdGlvbkl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbWQgcC0zIGJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntpdGVtLnRhc2t9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke2dldFByaW9yaXR5Q29sb3IoaXRlbS5wcmlvcml0eSl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5wcmlvcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEgdGV4dC14cyB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6LSf6LSj5Lq6OiB7aXRlbS5hc3NpZ25lZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uZGVhZGxpbmUgJiYgPHNwYW4gY2xhc3NOYW1lPVwibWwtM1wiPuaIquatojoge2l0ZW0uZGVhZGxpbmV9PC9zcGFuPn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAge21lZXRpbmdNaW51dGVzLm5leHRTdGVwcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSBtYi0yXCI+5LiL5LiA5q2l6K6h5YiSPC9oND5cbiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHttZWV0aW5nTWludXRlcy5uZXh0U3RlcHMubWFwKChzdGVwLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwIG10LTFcIj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGVwfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMiB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTQgb3BhY2l0eS01MFwiIC8+XG4gICAgICAgICAgICAgICAgPHA+5Zyo5bem5L6n6L6T5YWl5Lya6K6u5YaF5a655ZCO77yM54K55Ye755Sf5oiQ5oyJ6ZKuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJUZXh0YXJlYSIsIklucHV0IiwiUHJvZ3Jlc3MiLCJPbGxhbWFTZXJ2aWNlIiwiT2xsYW1hU3RhdHVzIiwiRmlsZVRleHQiLCJXYW5kMiIsIkRvd25sb2FkIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIk1lZXRpbmdNaW51dGVzR2VuZXJhdG9yIiwibWVldGluZ1RleHQiLCJzZXRNZWV0aW5nVGV4dCIsImlzR2VuZXJhdGluZyIsInNldElzR2VuZXJhdGluZyIsIm1lZXRpbmdNaW51dGVzIiwic2V0TWVldGluZ01pbnV0ZXMiLCJlcnJvciIsInNldEVycm9yIiwiZGVtb01vZGUiLCJzZXREZW1vTW9kZSIsInNlbGVjdGVkTW9kZWwiLCJzZXRTZWxlY3RlZE1vZGVsIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsImdlbmVyYXRpb25Qcm9ncmVzcyIsInNldEdlbmVyYXRpb25Qcm9ncmVzcyIsInByb2dyZXNzVmFsdWUiLCJzZXRQcm9ncmVzc1ZhbHVlIiwic3RyZWFtQ29udGVudCIsInNldFN0cmVhbUNvbnRlbnQiLCJvbGxhbWFTZXJ2aWNlIiwiaGFuZGxlRmlsZVVwbG9hZCIsImV2ZW50IiwiZmlsZSIsInRhcmdldCIsImZpbGVzIiwidHlwZSIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJlIiwiY29udGVudCIsInJlc3VsdCIsInJlYWRBc1RleHQiLCJnZW5lcmF0ZU1pbnV0ZXMiLCJ0cmltIiwic2VydmljZSIsIm1pbnV0ZXMiLCJnZW5lcmF0ZU1lZXRpbmdNaW51dGVzIiwic3RlcCIsInByZXYiLCJpbmNsdWRlcyIsIm5ld1Byb2dyZXNzIiwibGVuZ3RoIiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwiZG93bmxvYWRNaW51dGVzIiwidGl0bGUiLCJkYXRlIiwicGFydGljaXBhbnRzIiwiam9pbiIsInN1bW1hcnkiLCJrZXlEZWNpc2lvbnMiLCJtYXAiLCJkZWNpc2lvbiIsImFjdGlvbkl0ZW1zIiwiaXRlbSIsInRhc2siLCJhc3NpZ25lZSIsInByaW9yaXR5IiwiZGVhZGxpbmUiLCJuZXh0U3RlcHMiLCJibG9iIiwiQmxvYiIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImEiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJyZXBsYWNlIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsInJldm9rZU9iamVjdFVSTCIsImdldFByaW9yaXR5Q29sb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJsYWJlbCIsImlucHV0IiwiY2hlY2tlZCIsIm9uQ2hhbmdlIiwiaHRtbEZvciIsInNlbGVjdCIsImlkIiwidmFsdWUiLCJvcHRpb24iLCJhY2NlcHQiLCJwbGFjZWhvbGRlciIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInNwYW4iLCJNYXRoIiwicm91bmQiLCJpbmRleCIsInZhcmlhbnQiLCJzaXplIiwiaDMiLCJoNCIsInVsIiwibGkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MeetingMinutesGenerator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OllamaStatus.tsx":
/*!*****************************************!*\
  !*** ./src/components/OllamaStatus.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OllamaStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ollama */ \"(ssr)/./src/lib/ollama.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,RefreshCw,Server,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction OllamaStatus({ demoMode = false }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"OllamaStatus.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_4__.OllamaService()\n    }[\"OllamaStatus.useState\"]);\n    const checkConnection = async ()=>{\n        setIsChecking(true);\n        try {\n            if (demoMode) {\n                setIsConnected(true);\n                setModels([\n                    '演示模式 (无需真实模型)'\n                ]);\n            } else {\n                const connected = await ollamaService.checkConnection();\n                setIsConnected(connected);\n                if (connected) {\n                    const availableModels = await ollamaService.listModels();\n                    setModels(availableModels);\n                } else {\n                    setModels([]);\n                }\n            }\n        } catch (error) {\n            setIsConnected(false);\n            setModels([]);\n        } finally{\n            setIsChecking(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OllamaStatus.useEffect\": ()=>{\n            checkConnection();\n        }\n    }[\"OllamaStatus.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        \"Ollama 服务状态\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: checkConnection,\n                            disabled: isChecking,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"ml-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: `h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                \"刷新\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isConnected === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4 bg-gray-300 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this) : isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_RefreshCw_Server_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: isConnected === null ? '检查连接中...' : isConnected ? 'Ollama 服务已连接' : 'Ollama 服务未连接'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        isConnected && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: \"可用模型:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md\",\n                                            children: model\n                                        }, model, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-md p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium mb-2\",\n                                    children: \"如何启动 Ollama:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal list-inside space-y-1 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"确保已安装 Ollama: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"curl -fsSL https://ollama.ai/install.sh | sh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"启动 Ollama 服务: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"ollama serve\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"下载推荐模型: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-gray-100 px-1 rounded\",\n                                                    children: \"ollama pull llama3.1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 29\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\OllamaStatus.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OllamaStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiRDpcXHByXFxodG1sXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/progress.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/progress.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full w-full flex-1 bg-primary transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 18,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nProgress.displayName = \"Progress\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9wcm9ncmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBRWhDLE1BQU1FLHlCQUFXRiw2Q0FBZ0IsQ0FLL0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLEtBQUssRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUNqQyw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEgsV0FBV0gsOENBQUVBLENBQ1gsaUVBQ0FHO1FBRUQsR0FBR0UsS0FBSztrQkFFVCw0RUFBQ0U7WUFDQ0osV0FBVTtZQUNWSyxPQUFPO2dCQUFFQyxXQUFXLENBQUMsWUFBWSxFQUFFLE1BQU9MLENBQUFBLFNBQVMsR0FBRyxFQUFFLENBQUM7WUFBQzs7Ozs7Ozs7Ozs7QUFJaEVILFNBQVNTLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcY29tcG9uZW50c1xcdWlcXHByb2dyZXNzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBQcm9ncmVzcyA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4gJiB7XG4gICAgdmFsdWU/OiBudW1iZXJcbiAgfVxuPigoeyBjbGFzc05hbWUsIHZhbHVlLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGgtNCB3LWZ1bGwgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtZnVsbCBiZy1zZWNvbmRhcnlcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICA+XG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPVwiaC1mdWxsIHctZnVsbCBmbGV4LTEgYmctcHJpbWFyeSB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICBzdHlsZT17eyB0cmFuc2Zvcm06IGB0cmFuc2xhdGVYKC0kezEwMCAtICh2YWx1ZSB8fCAwKX0lKWAgfX1cbiAgICAvPlxuICA8L2Rpdj5cbikpXG5Qcm9ncmVzcy5kaXNwbGF5TmFtZSA9IFwiUHJvZ3Jlc3NcIlxuXG5leHBvcnQgeyBQcm9ncmVzcyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlByb2dyZXNzIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInZhbHVlIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJzdHlsZSIsInRyYW5zZm9ybSIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHlCQUFXRiw2Q0FBZ0IsQ0FDL0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4QixxQkFDRSw4REFBQ0M7UUFDQ0gsV0FBV0gsOENBQUVBLENBQ1gsd1NBQ0FHO1FBRUZFLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkgsU0FBU00sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIkQ6XFxwclxcaHRtbFxcc3JjXFxjb21wb25lbnRzXFx1aVxcdGV4dGFyZWEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgVGV4dGFyZWFQcm9wc1xuICBleHRlbmRzIFJlYWN0LlRleHRhcmVhSFRNTEF0dHJpYnV0ZXM8SFRNTFRleHRBcmVhRWxlbWVudD4ge31cblxuY29uc3QgVGV4dGFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQsIFRleHRhcmVhUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8dGV4dGFyZWFcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggbWluLWgtWzgwcHhdIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5UZXh0YXJlYS5kaXNwbGF5TmFtZSA9IFwiVGV4dGFyZWFcIlxuXG5leHBvcnQgeyBUZXh0YXJlYSB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlRleHRhcmVhIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwidGV4dGFyZWEiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n    async generateMeetingMinutes(meetingText, onProgress) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutesWithProgress(meetingText, onProgress);\n        }\n        const prompt = `\n你是一个专业的会议纪要分析专家。请仔细分析以下会议内容，并生成结构化的会议纪要。\n\n请按照以下要求进行分析：\n1. 仔细识别会议中的关键信息\n2. 提取所有参与者姓名和职位\n3. 总结会议的核心内容和目标\n4. 识别所有明确的决策和决定\n5. 提取所有行动项，包括负责人和时间要求\n6. 分析任务的优先级（根据紧急程度和重要性）\n\n请以JSON格式返回结果，包含以下字段：\n- title: 会议标题（如果没有明确标题，请根据内容生成一个合适的标题）\n- date: 会议日期（格式：YYYY-MM-DD，如果没有明确日期，使用今天的日期）\n- participants: 参与者列表（数组，包含姓名和职位信息）\n- summary: 会议摘要（3-4句话，概括会议的主要内容和成果）\n- keyDecisions: 关键决策列表（数组，每个决策用简洁明确的语言描述）\n- actionItems: 行动项列表（数组，每个对象包含：task（具体任务描述）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low））\n- nextSteps: 下一步计划（数组，列出后续需要进行的工作）\n\n会议内容：\n${meetingText}\n\n请确保返回的是有效的JSON格式，不要包含markdown代码块标记或其他额外文本。JSON应该直接开始和结束。\n`;\n        try {\n            onProgress?.('正在连接 AI 服务...', '');\n            const response = await fetch(`${this.baseUrl}/api/generate`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: true\n                })\n            });\n            onProgress?.('AI 正在分析会议内容...', '');\n            if (!response.ok) {\n                throw new Error(`Ollama API error: ${response.status}`);\n            }\n            onProgress?.('正在处理 AI 响应...', '');\n            // 处理流式响应\n            let fullResponse = '';\n            const reader = response.body?.getReader();\n            const decoder = new TextDecoder();\n            if (reader) {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    const chunk = decoder.decode(value);\n                    const lines = chunk.split('\\n').filter((line)=>line.trim());\n                    for (const line of lines){\n                        try {\n                            const data = JSON.parse(line);\n                            if (data.response) {\n                                fullResponse += data.response;\n                                onProgress?.('AI 正在生成内容...', fullResponse.slice(0, 100) + '...');\n                            }\n                            if (data.done) break;\n                        } catch (e) {\n                        // 忽略解析错误的行\n                        }\n                    }\n                }\n            } else {\n                // 回退到非流式响应\n                const data = await response.json();\n                fullResponse = data.response;\n            }\n            onProgress?.('正在解析生成结果...', '');\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = fullResponse.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    async generateDemoMinutesWithProgress(meetingText, onProgress) {\n        const steps = [\n            {\n                step: '正在分析会议内容...',\n                delay: 800\n            },\n            {\n                step: '提取参与者信息...',\n                delay: 600\n            },\n            {\n                step: '识别关键决策...',\n                delay: 700\n            },\n            {\n                step: '分析行动项...',\n                delay: 900\n            },\n            {\n                step: '生成会议摘要...',\n                delay: 500\n            },\n            {\n                step: '整理最终结果...',\n                delay: 400\n            }\n        ];\n        for (const { step, delay } of steps){\n            onProgress?.(step, '');\n            await new Promise((resolve)=>setTimeout(resolve, delay));\n        }\n        return this.generateDemoMinutes(meetingText);\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`);\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`);\n            if (!response.ok) return [];\n            const data = await response.json();\n            return data.models?.map((model)=>model.name) || [];\n        } catch  {\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/ollama.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXHByXFxodG1sXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY5NDBjNzdjOTA1N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: '智能会议纪要生成器',\n    description: '使用 AI 技术自动生成结构化的会议纪要和行动项'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\pr\\\\html\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFBYUo7Ozs7Ozs7Ozs7O0FBR25DIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ+aZuuiDveS8muiurue6quimgeeUn+aIkOWZqCcsXG4gIGRlc2NyaXB0aW9uOiAn5L2/55SoIEFJIOaKgOacr+iHquWKqOeUn+aIkOe7k+aehOWMlueahOS8muiurue6quimgeWSjOihjOWKqOmhuScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_MeetingMinutesGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/MeetingMinutesGenerator */ \"(rsc)/./src/components/MeetingMinutesGenerator.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MeetingMinutesGenerator__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\pr\\\\html\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTJFO0FBRTVELFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFLQyxXQUFVO2tCQUNkLDRFQUFDSCwyRUFBdUJBOzs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTWVldGluZ01pbnV0ZXNHZW5lcmF0b3IgZnJvbSAnQC9jb21wb25lbnRzL01lZXRpbmdNaW51dGVzR2VuZXJhdG9yJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby0xMDBcIj5cbiAgICAgIDxNZWV0aW5nTWludXRlc0dlbmVyYXRvciAvPlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJNZWV0aW5nTWludXRlc0dlbmVyYXRvciIsIkhvbWUiLCJtYWluIiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/MeetingMinutesGenerator.tsx":
/*!****************************************************!*\
  !*** ./src/components/MeetingMinutesGenerator.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\pr\\html\\src\\components\\MeetingMinutesGenerator.tsx",
"default",
));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cpr%5Chtml%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cpr%5Chtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();