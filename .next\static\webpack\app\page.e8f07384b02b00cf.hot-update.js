"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutes(meetingText);\n        }\n        const prompt = \"\\n请分析以下会议内容，并生成结构化的会议纪要。请以JSON格式返回结果，包含以下字段：\\n\\n1. title: 会议标题\\n2. date: 会议日期（如果文本中没有明确日期，使用今天的日期）\\n3. participants: 参与者列表\\n4. summary: 会议摘要（2-3句话）\\n5. keyDecisions: 关键决策列表\\n6. actionItems: 行动项列表，每个包含 task（任务）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low）\\n7. nextSteps: 下一步计划\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含任何其他文本。\\n\");\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    async checkConnection() {\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'llama3.2:3b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});