"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MeetingMinutesGenerator.tsx":
/*!****************************************************!*\
  !*** ./src/components/MeetingMinutesGenerator.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MeetingMinutesGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/ollama */ \"(app-pages-browser)/./src/lib/ollama.ts\");\n/* harmony import */ var _components_OllamaStatus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/OllamaStatus */ \"(app-pages-browser)/./src/components/OllamaStatus.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-2.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Download,FileText,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MeetingMinutesGenerator() {\n    _s();\n    const [meetingText, setMeetingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [meetingMinutes, setMeetingMinutes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [demoMode, setDemoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('qwq:32b');\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [progressValue, setProgressValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [streamContent, setStreamContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [ollamaService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"MeetingMinutesGenerator.useState\": ()=>new _lib_ollama__WEBPACK_IMPORTED_MODULE_6__.OllamaService('http://localhost:11434', selectedModel, demoMode)\n    }[\"MeetingMinutesGenerator.useState\"]);\n    const handleFileUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file && file.type === 'text/plain') {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const content = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setMeetingText(content);\n            };\n            reader.readAsText(file);\n        } else {\n            setError('请上传 .txt 格式的文件');\n        }\n    };\n    const generateMinutes = async ()=>{\n        if (!meetingText.trim()) {\n            setError('请输入会议内容');\n            return;\n        }\n        setIsGenerating(true);\n        setError(null);\n        setCurrentStep('');\n        setGenerationProgress([]);\n        setProgressValue(0);\n        setStreamContent('');\n        setMeetingMinutes(null);\n        try {\n            // 创建新的服务实例以使用当前的演示模式和模型设置\n            const service = new _lib_ollama__WEBPACK_IMPORTED_MODULE_6__.OllamaService('http://localhost:11434', selectedModel, demoMode);\n            const minutes = await service.generateMeetingMinutes(meetingText, (step, content)=>{\n                setCurrentStep(step);\n                setStreamContent(content);\n                setGenerationProgress((prev)=>{\n                    if (!prev.includes(step)) {\n                        const newProgress = [\n                            ...prev,\n                            step\n                        ];\n                        setProgressValue(newProgress.length / 6 * 100); // 假设有6个步骤\n                        return newProgress;\n                    }\n                    return prev;\n                });\n            });\n            setCurrentStep('✅ 生成完成！');\n            setMeetingMinutes(minutes);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : '生成会议纪要时发生错误');\n            setCurrentStep('');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const downloadMinutes = ()=>{\n        if (!meetingMinutes) return;\n        const content = \"\\n# \".concat(meetingMinutes.title, \"\\n\\n**日期：** \").concat(meetingMinutes.date, \"\\n\\n**参与者：** \").concat(meetingMinutes.participants.join(', '), \"\\n\\n## 会议摘要\\n\").concat(meetingMinutes.summary, \"\\n\\n## 关键决策\\n\").concat(meetingMinutes.keyDecisions.map((decision)=>\"- \".concat(decision)).join('\\n'), \"\\n\\n## 行动项\\n\").concat(meetingMinutes.actionItems.map((item)=>\"- **\".concat(item.task, \"** (负责人: \").concat(item.assignee, \", 优先级: \").concat(item.priority).concat(item.deadline ? \", 截止日期: \".concat(item.deadline) : '', \")\")).join('\\n'), \"\\n\\n## 下一步计划\\n\").concat(meetingMinutes.nextSteps.map((step)=>\"- \".concat(step)).join('\\n'), \"\\n\");\n        const blob = new Blob([\n            content\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"\".concat(meetingMinutes.title.replace(/\\s+/g, '_'), \"_\").concat(meetingMinutes.date, \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'low':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"智能会议纪要生成器\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"使用本地 AI 技术自动生成结构化的会议纪要\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-6 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center gap-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: demoMode,\n                                        onChange: (e)=>setDemoMode(e.target.checked),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"演示模式（无需 Ollama）\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            !demoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"model-select\",\n                                        children: \"AI 模型:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"model-select\",\n                                        value: selectedModel,\n                                        onChange: (e)=>setSelectedModel(e.target.value),\n                                        className: \"px-2 py-1 border border-gray-300 rounded text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwq:32b\",\n                                                children: \"qwq:32b (推荐)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwen2.5:7b\",\n                                                children: \"qwen2.5:7b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qwen2.5:0.5b\",\n                                                children: \"qwen2.5:0.5b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"llama3.1:8b\",\n                                                children: \"llama3.1:8b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"llama3.2:3b\",\n                                                children: \"llama3.2:3b\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OllamaStatus__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                demoMode: demoMode\n            }, void 0, false, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"会议内容输入\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"粘贴会议文本或上传文本文件\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"上传文本文件\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"file-upload\",\n                                                type: \"file\",\n                                                accept: \".txt\",\n                                                onChange: handleFileUpload,\n                                                className: \"cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                placeholder: \"在此粘贴会议内容，包括讨论要点、决策和任务分配...\",\n                                                value: meetingText,\n                                                onChange: (e)=>setMeetingText(e.target.value),\n                                                className: \"min-h-[300px] resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 right-2 text-xs text-gray-400\",\n                                                children: [\n                                                    meetingText.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: generateMinutes,\n                                        disabled: isGenerating || !meetingText.trim(),\n                                        className: \"w-full\",\n                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"正在生成纪要...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"生成会议纪要\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-50 border border-blue-200 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-700 font-medium\",\n                                                        children: \"正在生成会议纪要\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-600 mb-2\",\n                                                children: currentStep\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this),\n                                            generationProgress.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: generationProgress.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-xs text-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            step\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-700 text-sm\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"生成的会议纪要\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            meetingMinutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: downloadMinutes,\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"下载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"AI 生成的结构化会议纪要\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: meetingMinutes ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold mb-2\",\n                                                    children: meetingMinutes.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"日期: \",\n                                                        meetingMinutes.date\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                meetingMinutes.participants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"参与者: \",\n                                                        meetingMinutes.participants.join(', ')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"会议摘要\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700 bg-gray-50 p-3 rounded-md\",\n                                                    children: meetingMinutes.summary\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this),\n                                        meetingMinutes.keyDecisions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"关键决策\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.keyDecisions.map((decision, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                decision\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.actionItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"行动项\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: meetingMinutes.actionItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-md p-3 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: item.task\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(item.priority)),\n                                                                            children: item.priority\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1 text-xs text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"负责人: \",\n                                                                                item.assignee\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        item.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-3\",\n                                                                            children: [\n                                                                                \"截止: \",\n                                                                                item.deadline\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 47\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this),\n                                        meetingMinutes.nextSteps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"下一步计划\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: meetingMinutes.nextSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-700 flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mt-1\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                step\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Download_FileText_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"在左侧输入会议内容后，点击生成按钮\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pr\\\\html\\\\src\\\\components\\\\MeetingMinutesGenerator.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(MeetingMinutesGenerator, \"5B1bmkTCcuzTwhVOnDJhMl7bbVE=\");\n_c = MeetingMinutesGenerator;\nvar _c;\n$RefreshReg$(_c, \"MeetingMinutesGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MeetingMinutesGenerator.tsx\n"));

/***/ })

});