"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText, onProgress) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutesWithProgress(meetingText, onProgress);\n        }\n        const prompt = \"\\n你是一个专业的会议纪要分析专家。请仔细分析以下会议内容，并生成结构化的会议纪要。\\n\\n请按照以下要求进行分析：\\n1. 仔细识别会议中的关键信息\\n2. 提取所有参与者姓名和职位\\n3. 总结会议的核心内容和目标\\n4. 识别所有明确的决策和决定\\n5. 提取所有行动项，包括负责人和时间要求\\n6. 分析任务的优先级（根据紧急程度和重要性）\\n\\n请以JSON格式返回结果，包含以下字段：\\n- title: 会议标题（如果没有明确标题，请根据内容生成一个合适的标题）\\n- date: 会议日期（格式：YYYY-MM-DD，如果没有明确日期，使用今天的日期）\\n- participants: 参与者列表（数组，包含姓名和职位信息）\\n- summary: 会议摘要（3-4句话，概括会议的主要内容和成果）\\n- keyDecisions: 关键决策列表（数组，每个决策用简洁明确的语言描述）\\n- actionItems: 行动项列表（数组，每个对象包含：task（具体任务描述）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low））\\n- nextSteps: 下一步计划（数组，列出后续需要进行的工作）\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含markdown代码块标记或其他额外文本。JSON应该直接开始和结束。\\n\");\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});