"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutes(meetingText);\n        }\n        const prompt = \"\\n请分析以下会议内容，并生成结构化的会议纪要。请以JSON格式返回结果，包含以下字段：\\n\\n1. title: 会议标题\\n2. date: 会议日期（如果文本中没有明确日期，使用今天的日期）\\n3. participants: 参与者列表\\n4. summary: 会议摘要（2-3句话）\\n5. keyDecisions: 关键决策列表\\n6. actionItems: 行动项列表，每个包含 task（任务）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low）\\n7. nextSteps: 下一步计划\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含任何其他文本。\\n\");\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'llama3.2:3b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvb2xsYW1hLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxnQkFBZ0I7QUF1QlQsTUFBTUE7SUFXWCxNQUFNQyx1QkFBdUJDLFdBQW1CLEVBQTJCO1FBQ3pFLGlCQUFpQjtRQUNqQixJQUFJLElBQUksQ0FBQ0MsUUFBUSxFQUFFO1lBQ2pCLE9BQU8sSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQ0Y7UUFDbEM7UUFDQSxNQUFNRyxTQUFTLGtUQVlMLE9BQVpILGFBQVk7UUFLVixJQUFJO1lBQ0YsTUFBTUksV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDQyxPQUFPLEVBQUMsa0JBQWdCO2dCQUMzREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxPQUFPLElBQUksQ0FBQ0EsS0FBSztvQkFDakJULFFBQVFBO29CQUNSVSxRQUFRO2dCQUNWO1lBQ0Y7WUFFQSxJQUFJLENBQUNULFNBQVNVLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNLHFCQUFxQyxPQUFoQlgsU0FBU1ksTUFBTTtZQUN0RDtZQUVBLE1BQU1DLE9BQXVCLE1BQU1iLFNBQVNjLElBQUk7WUFFaEQsZUFBZTtZQUNmLElBQUk7Z0JBQ0YsTUFBTUMsa0JBQWtCRixLQUFLYixRQUFRLENBQUNnQixJQUFJO2dCQUMxQyx1QkFBdUI7Z0JBQ3ZCLE1BQU1DLFlBQVlGLGdCQUFnQkcsS0FBSyxDQUFDO2dCQUN4QyxNQUFNQyxhQUFhRixZQUFZQSxTQUFTLENBQUMsRUFBRSxHQUFHRjtnQkFFOUMsTUFBTUssaUJBQWlDZCxLQUFLZSxLQUFLLENBQUNGO2dCQUVsRCxTQUFTO2dCQUNULElBQUksQ0FBQ0MsZUFBZUUsS0FBSyxFQUFFRixlQUFlRSxLQUFLLEdBQUc7Z0JBQ2xELElBQUksQ0FBQ0YsZUFBZUcsSUFBSSxFQUFFSCxlQUFlRyxJQUFJLEdBQUcsSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQ3RGLElBQUksQ0FBQ04sZUFBZU8sWUFBWSxFQUFFUCxlQUFlTyxZQUFZLEdBQUcsRUFBRTtnQkFDbEUsSUFBSSxDQUFDUCxlQUFlUSxPQUFPLEVBQUVSLGVBQWVRLE9BQU8sR0FBRztnQkFDdEQsSUFBSSxDQUFDUixlQUFlUyxZQUFZLEVBQUVULGVBQWVTLFlBQVksR0FBRyxFQUFFO2dCQUNsRSxJQUFJLENBQUNULGVBQWVVLFdBQVcsRUFBRVYsZUFBZVUsV0FBVyxHQUFHLEVBQUU7Z0JBQ2hFLElBQUksQ0FBQ1YsZUFBZVcsU0FBUyxFQUFFWCxlQUFlVyxTQUFTLEdBQUcsRUFBRTtnQkFFNUQsT0FBT1g7WUFDVCxFQUFFLE9BQU9ZLFlBQVk7Z0JBQ25CQyxRQUFRQyxLQUFLLENBQUMsdUJBQXVCRjtnQkFDckMsZ0JBQWdCO2dCQUNoQixPQUFPLElBQUksQ0FBQ0cscUJBQXFCLENBQUN2QztZQUNwQztRQUNGLEVBQUUsT0FBT3NDLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsTUFBTSxJQUFJdkIsTUFBTTtRQUNsQjtJQUNGO0lBRVF3QixzQkFBc0J2QyxXQUFtQixFQUFrQjtRQUNqRSxPQUFPO1lBQ0wwQixPQUFPO1lBQ1BDLE1BQU0sSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDNUNDLGNBQWMsRUFBRTtZQUNoQkMsU0FBUztZQUNUQyxjQUFjO2dCQUFDO2FBQVk7WUFDM0JDLGFBQWE7Z0JBQUM7b0JBQ1pNLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLFVBQVU7Z0JBQ1o7YUFBRTtZQUNGUCxXQUFXO2dCQUFDO2FBQWE7UUFDM0I7SUFDRjtJQUVRakMsb0JBQW9CRixXQUFtQixFQUFrQjtRQUMvRCxlQUFlO1FBQ2YsTUFBTStCLGVBQWUsSUFBSSxDQUFDWSxtQkFBbUIsQ0FBQzNDO1FBQzlDLE1BQU00QyxZQUFZLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUM3QztRQUN4QyxNQUFNOEMsVUFBVSxJQUFJLENBQUNDLGNBQWMsQ0FBQy9DO1FBRXBDLE9BQU87WUFDTDBCLE9BQU87WUFDUEMsTUFBTSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM1Q0MsY0FBY0E7WUFDZEMsU0FBUztZQUNUQyxjQUFjVztZQUNkVixhQUFhWTtZQUNiWCxXQUFXO2dCQUFDO2dCQUFZO2dCQUFZO2FBQVk7UUFDbEQ7SUFDRjtJQUVRUSxvQkFBb0JLLElBQVksRUFBWTtRQUNsRCxNQUFNakIsZUFBZSxFQUFFO1FBQ3ZCLE1BQU1rQixXQUFXO1lBQ2Y7WUFDQTtZQUNBO1NBQ0Q7UUFFREEsU0FBU0MsT0FBTyxDQUFDQyxDQUFBQTtZQUNmLE1BQU1DLFVBQVVKLEtBQUsxQixLQUFLLENBQUM2QjtZQUMzQixJQUFJQyxTQUFTO2dCQUNYQSxRQUFRRixPQUFPLENBQUM1QixDQUFBQTtvQkFDZCxNQUFNK0IsUUFBUS9CLE1BQU1RLEtBQUssQ0FBQyxTQUFTd0IsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLbkMsSUFBSTtvQkFDeERXLGFBQWF5QixJQUFJLElBQUlIO2dCQUN2QjtZQUNGO1FBQ0Y7UUFFQSxPQUFPdEIsYUFBYTBCLE1BQU0sR0FBRyxJQUFJMUIsYUFBYTJCLEtBQUssQ0FBQyxHQUFHLEtBQUs7WUFBQztZQUFNO1lBQU07U0FBSztJQUNoRjtJQUVRYixpQkFBaUJHLElBQVksRUFBWTtRQUMvQyxNQUFNSixZQUFZLEVBQUU7UUFDcEIsTUFBTUssV0FBVztZQUNmO1lBQ0E7WUFDQTtTQUNEO1FBRURBLFNBQVNDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDZixNQUFNQyxVQUFVSixLQUFLMUIsS0FBSyxDQUFDNkI7WUFDM0IsSUFBSUMsU0FBUztnQkFDWFIsVUFBVVksSUFBSSxJQUFJSixRQUFRRSxHQUFHLENBQUNoQyxDQUFBQSxRQUFTQSxNQUFNcUMsT0FBTyxDQUFDLGVBQWUsSUFBSXZDLElBQUk7WUFDOUU7UUFDRjtRQUVBLE9BQU93QixVQUFVYSxNQUFNLEdBQUcsSUFBSWIsWUFBWTtZQUFDO1lBQVc7WUFBVTtTQUFTO0lBQzNFO0lBRVFHLGVBQWVDLElBQVksRUFBbUc7UUFDcEksTUFBTUYsVUFBVSxFQUFFO1FBQ2xCLE1BQU1jLGlCQUFpQjtZQUNyQjtZQUNBO1NBQ0Q7UUFFREEsZUFBZVYsT0FBTyxDQUFDQyxDQUFBQTtZQUNyQixNQUFNQyxVQUFVO21CQUFJSixLQUFLYSxRQUFRLENBQUNWO2FBQVM7WUFDM0NDLFFBQVFGLE9BQU8sQ0FBQzVCLENBQUFBO2dCQUNkLElBQUlBLEtBQUssQ0FBQyxFQUFFLElBQUlBLEtBQUssQ0FBQyxFQUFFLEVBQUU7b0JBQ3hCd0IsUUFBUVUsSUFBSSxDQUFDO3dCQUNYaEIsTUFBTWxCLEtBQUssQ0FBQyxFQUFFLENBQUNGLElBQUk7d0JBQ25CcUIsVUFBVW5CLEtBQUssQ0FBQyxFQUFFLENBQUNGLElBQUk7d0JBQ3ZCc0IsVUFBVTtvQkFDWjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxJQUFJSSxRQUFRVyxNQUFNLEtBQUssR0FBRztZQUN4QlgsUUFBUVUsSUFBSSxDQUNWO2dCQUFFaEIsTUFBTTtnQkFBWUMsVUFBVTtnQkFBTUMsVUFBVTtZQUFnQixHQUM5RDtnQkFBRUYsTUFBTTtnQkFBWUMsVUFBVTtnQkFBTUMsVUFBVTtZQUFrQixHQUNoRTtnQkFBRUYsTUFBTTtnQkFBVUMsVUFBVTtnQkFBTUMsVUFBVTtZQUFlO1FBRS9EO1FBRUEsT0FBT0k7SUFDVDtJQUVBLE1BQU1nQixrQkFBb0M7UUFDeEMsSUFBSSxJQUFJLENBQUM3RCxRQUFRLEVBQUUsT0FBTztRQUUxQixJQUFJO1lBQ0YsTUFBTUcsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDQyxPQUFPLEVBQUM7WUFDN0MsT0FBT0YsU0FBU1UsRUFBRTtRQUNwQixFQUFFLFVBQU07WUFDTixPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1pRCxhQUFnQztRQUNwQyxJQUFJO2dCQUtLOUM7WUFKUCxNQUFNYixXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYixJQUFJLENBQUNDLE9BQU8sRUFBQztZQUM3QyxJQUFJLENBQUNGLFNBQVNVLEVBQUUsRUFBRSxPQUFPLEVBQUU7WUFFM0IsTUFBTUcsT0FBTyxNQUFNYixTQUFTYyxJQUFJO1lBQ2hDLE9BQU9ELEVBQUFBLGVBQUFBLEtBQUsrQyxNQUFNLGNBQVgvQyxtQ0FBQUEsYUFBYXFDLEdBQUcsQ0FBQyxDQUFDMUMsUUFBZUEsTUFBTTJDLElBQUksTUFBSyxFQUFFO1FBQzNELEVBQUUsVUFBTTtZQUNOLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUF6TUFVLFlBQVkzRCxVQUFVLHdCQUF3QixFQUFFTSxRQUFRLGFBQWEsRUFBRVgsV0FBVyxLQUFLLENBQUU7UUFDdkYsSUFBSSxDQUFDSyxPQUFPLEdBQUdBO1FBQ2YsSUFBSSxDQUFDTSxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDWCxRQUFRLEdBQUdBO0lBQ2xCO0FBc01GIiwic291cmNlcyI6WyJEOlxccHJcXGh0bWxcXHNyY1xcbGliXFxvbGxhbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gT2xsYW1hIEFQSSDpm4bmiJBcbmV4cG9ydCBpbnRlcmZhY2UgT2xsYW1hUmVzcG9uc2Uge1xuICBtb2RlbDogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHJlc3BvbnNlOiBzdHJpbmc7XG4gIGRvbmU6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVldGluZ01pbnV0ZXMge1xuICB0aXRsZTogc3RyaW5nO1xuICBkYXRlOiBzdHJpbmc7XG4gIHBhcnRpY2lwYW50czogc3RyaW5nW107XG4gIHN1bW1hcnk6IHN0cmluZztcbiAga2V5RGVjaXNpb25zOiBzdHJpbmdbXTtcbiAgYWN0aW9uSXRlbXM6IEFycmF5PHtcbiAgICB0YXNrOiBzdHJpbmc7XG4gICAgYXNzaWduZWU6IHN0cmluZztcbiAgICBkZWFkbGluZT86IHN0cmluZztcbiAgICBwcmlvcml0eTogJ2hpZ2gnIHwgJ21lZGl1bScgfCAnbG93JztcbiAgfT47XG4gIG5leHRTdGVwczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBjbGFzcyBPbGxhbWFTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBiYXNlVXJsOiBzdHJpbmc7XG4gIHByaXZhdGUgbW9kZWw6IHN0cmluZztcbiAgcHJpdmF0ZSBkZW1vTW9kZTogYm9vbGVhbjtcblxuICBjb25zdHJ1Y3RvcihiYXNlVXJsID0gJ2h0dHA6Ly9sb2NhbGhvc3Q6MTE0MzQnLCBtb2RlbCA9ICdsbGFtYTMuMjozYicsIGRlbW9Nb2RlID0gZmFsc2UpIHtcbiAgICB0aGlzLmJhc2VVcmwgPSBiYXNlVXJsO1xuICAgIHRoaXMubW9kZWwgPSBtb2RlbDtcbiAgICB0aGlzLmRlbW9Nb2RlID0gZGVtb01vZGU7XG4gIH1cblxuICBhc3luYyBnZW5lcmF0ZU1lZXRpbmdNaW51dGVzKG1lZXRpbmdUZXh0OiBzdHJpbmcpOiBQcm9taXNlPE1lZXRpbmdNaW51dGVzPiB7XG4gICAgLy8g5aaC5p6c5piv5ryU56S65qih5byP77yM6L+U5Zue5qih5ouf5pWw5o2uXG4gICAgaWYgKHRoaXMuZGVtb01vZGUpIHtcbiAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlRGVtb01pbnV0ZXMobWVldGluZ1RleHQpO1xuICAgIH1cbiAgICBjb25zdCBwcm9tcHQgPSBgXG7or7fliIbmnpDku6XkuIvkvJrorq7lhoXlrrnvvIzlubbnlJ/miJDnu5PmnoTljJbnmoTkvJrorq7nuqropoHjgILor7fku6VKU09O5qC85byP6L+U5Zue57uT5p6c77yM5YyF5ZCr5Lul5LiL5a2X5q6177yaXG5cbjEuIHRpdGxlOiDkvJrorq7moIfpophcbjIuIGRhdGU6IOS8muiuruaXpeacn++8iOWmguaenOaWh+acrOS4reayoeacieaYjuehruaXpeacn++8jOS9v+eUqOS7iuWkqeeahOaXpeacn++8iVxuMy4gcGFydGljaXBhbnRzOiDlj4LkuI7ogIXliJfooahcbjQuIHN1bW1hcnk6IOS8muiuruaRmOimge+8iDItM+WPpeivne+8iVxuNS4ga2V5RGVjaXNpb25zOiDlhbPplK7lhrPnrZbliJfooahcbjYuIGFjdGlvbkl0ZW1zOiDooYzliqjpobnliJfooajvvIzmr4/kuKrljIXlkKsgdGFza++8iOS7u+WKoe+8ieOAgWFzc2lnbmVl77yI6LSf6LSj5Lq677yJ44CBZGVhZGxpbmXvvIjmiKrmraLml6XmnJ/vvIzlj6/pgInvvInjgIFwcmlvcml0ee+8iOS8mOWFiOe6p++8mmhpZ2gvbWVkaXVtL2xvd++8iVxuNy4gbmV4dFN0ZXBzOiDkuIvkuIDmraXorqHliJJcblxu5Lya6K6u5YaF5a6577yaXG4ke21lZXRpbmdUZXh0fVxuXG7or7fnoa7kv53ov5Tlm57nmoTmmK/mnInmlYjnmoRKU09O5qC85byP77yM5LiN6KaB5YyF5ZCr5Lu75L2V5YW25LuW5paH5pys44CCXG5gO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9hcGkvZ2VuZXJhdGVgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIG1vZGVsOiB0aGlzLm1vZGVsLFxuICAgICAgICAgIHByb21wdDogcHJvbXB0LFxuICAgICAgICAgIHN0cmVhbTogZmFsc2UsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBPbGxhbWEgQVBJIGVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YTogT2xsYW1hUmVzcG9uc2UgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIC8vIOWwneivleino+aekCBKU09OIOWTjeW6lFxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY2xlYW5lZFJlc3BvbnNlID0gZGF0YS5yZXNwb25zZS50cmltKCk7XG4gICAgICAgIC8vIOenu+mZpOWPr+iDveeahCBtYXJrZG93biDku6PnoIHlnZfmoIforrBcbiAgICAgICAgY29uc3QganNvbk1hdGNoID0gY2xlYW5lZFJlc3BvbnNlLm1hdGNoKC9gYGAoPzpqc29uKT9cXHMqKFtcXHNcXFNdKj8pXFxzKmBgYC8pO1xuICAgICAgICBjb25zdCBqc29uU3RyaW5nID0ganNvbk1hdGNoID8ganNvbk1hdGNoWzFdIDogY2xlYW5lZFJlc3BvbnNlO1xuICAgICAgICBcbiAgICAgICAgY29uc3QgbWVldGluZ01pbnV0ZXM6IE1lZXRpbmdNaW51dGVzID0gSlNPTi5wYXJzZShqc29uU3RyaW5nKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOmqjOivgeW/heimgeWtl+autVxuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLnRpdGxlKSBtZWV0aW5nTWludXRlcy50aXRsZSA9ICfkvJrorq7nuqropoEnO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLmRhdGUpIG1lZXRpbmdNaW51dGVzLmRhdGUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy5wYXJ0aWNpcGFudHMpIG1lZXRpbmdNaW51dGVzLnBhcnRpY2lwYW50cyA9IFtdO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLnN1bW1hcnkpIG1lZXRpbmdNaW51dGVzLnN1bW1hcnkgPSAn5Lya6K6u5pGY6KaB55Sf5oiQ5LitLi4uJztcbiAgICAgICAgaWYgKCFtZWV0aW5nTWludXRlcy5rZXlEZWNpc2lvbnMpIG1lZXRpbmdNaW51dGVzLmtleURlY2lzaW9ucyA9IFtdO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLmFjdGlvbkl0ZW1zKSBtZWV0aW5nTWludXRlcy5hY3Rpb25JdGVtcyA9IFtdO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLm5leHRTdGVwcykgbWVldGluZ01pbnV0ZXMubmV4dFN0ZXBzID0gW107XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gbWVldGluZ01pbnV0ZXM7XG4gICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0pTT04gcGFyc2luZyBlcnJvcjonLCBwYXJzZUVycm9yKTtcbiAgICAgICAgLy8g5aaC5p6c6Kej5p6Q5aSx6LSl77yM6L+U5Zue5Z+65pys57uT5p6EXG4gICAgICAgIHJldHVybiB0aGlzLmNyZWF0ZUZhbGxiYWNrTWludXRlcyhtZWV0aW5nVGV4dCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ09sbGFtYSBzZXJ2aWNlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcign5peg5rOV6L+e5o6l5YiwIE9sbGFtYSDmnI3liqHjgILor7fnoa7kv50gT2xsYW1hIOato+WcqOi/kOihjOW5tuS4lOaooeWei+W3suS4i+i9veOAgicpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgY3JlYXRlRmFsbGJhY2tNaW51dGVzKG1lZXRpbmdUZXh0OiBzdHJpbmcpOiBNZWV0aW5nTWludXRlcyB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRpdGxlOiAn5Lya6K6u57qq6KaBJyxcbiAgICAgIGRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgcGFydGljaXBhbnRzOiBbXSxcbiAgICAgIHN1bW1hcnk6ICfnlLHkuo5BSeacjeWKoeaaguaXtuS4jeWPr+eUqO+8jOivt+aJi+WKqOaVtOeQhuS8muiurue6quimgeOAgicsXG4gICAgICBrZXlEZWNpc2lvbnM6IFsn6K+35omL5Yqo5re75Yqg5YWz6ZSu5Yaz562WJ10sXG4gICAgICBhY3Rpb25JdGVtczogW3tcbiAgICAgICAgdGFzazogJ+ivt+aJi+WKqOa3u+WKoOihjOWKqOmhuScsXG4gICAgICAgIGFzc2lnbmVlOiAn5b6F5YiG6YWNJyxcbiAgICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nIGFzIGNvbnN0XG4gICAgICB9XSxcbiAgICAgIG5leHRTdGVwczogWyfor7fmiYvliqjmt7vliqDkuIvkuIDmraXorqHliJInXVxuICAgIH07XG4gIH1cblxuICBwcml2YXRlIGdlbmVyYXRlRGVtb01pbnV0ZXMobWVldGluZ1RleHQ6IHN0cmluZyk6IE1lZXRpbmdNaW51dGVzIHtcbiAgICAvLyDln7rkuo7ovpPlhaXmlofmnKznlJ/miJDmvJTnpLrmlbDmja5cbiAgICBjb25zdCBwYXJ0aWNpcGFudHMgPSB0aGlzLmV4dHJhY3RQYXJ0aWNpcGFudHMobWVldGluZ1RleHQpO1xuICAgIGNvbnN0IGRlY2lzaW9ucyA9IHRoaXMuZXh0cmFjdERlY2lzaW9ucyhtZWV0aW5nVGV4dCk7XG4gICAgY29uc3QgYWN0aW9ucyA9IHRoaXMuZXh0cmFjdEFjdGlvbnMobWVldGluZ1RleHQpO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHRpdGxlOiAn5Lya6K6u57qq6KaBICjmvJTnpLrmqKHlvI8pJyxcbiAgICAgIGRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgcGFydGljaXBhbnRzOiBwYXJ0aWNpcGFudHMsXG4gICAgICBzdW1tYXJ5OiAn6L+Z5piv5ryU56S65qih5byP55Sf5oiQ55qE5Lya6K6u5pGY6KaB44CC5pys5qyh5Lya6K6u6K6o6K665LqG6aG555uu6L+b5bGV44CB5YWz6ZSu5Yaz562W5ZKM5ZCO57ut6KGM5Yqo6K6h5YiS44CCJyxcbiAgICAgIGtleURlY2lzaW9uczogZGVjaXNpb25zLFxuICAgICAgYWN0aW9uSXRlbXM6IGFjdGlvbnMsXG4gICAgICBuZXh0U3RlcHM6IFsn57un57ut5o6o6L+b6aG555uu5byA5Y+RJywgJ+WHhuWkh+S4i+asoeS8muiuruiurueoiycsICfot5/ov5vooYzliqjpobnmiafooYzmg4XlhrUnXVxuICAgIH07XG4gIH1cblxuICBwcml2YXRlIGV4dHJhY3RQYXJ0aWNpcGFudHModGV4dDogc3RyaW5nKTogc3RyaW5nW10ge1xuICAgIGNvbnN0IHBhcnRpY2lwYW50cyA9IFtdO1xuICAgIGNvbnN0IHBhdHRlcm5zID0gW1xuICAgICAgL+WPguS4juiAhVvvvJo6XVxccyooW17jgIJcXG5dKykvZyxcbiAgICAgIC/kuI7kvJrkurrlkZhb77yaOl1cXHMqKFte44CCXFxuXSspL2csXG4gICAgICAvKFvlvKDmnY7njovotbXliJjpmYjmnajpu4TlkajlkLRdXFx3Kylb77yIKF0oW17vvIkpXSspW++8iSldL2dcbiAgICBdO1xuXG4gICAgcGF0dGVybnMuZm9yRWFjaChwYXR0ZXJuID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKHBhdHRlcm4pO1xuICAgICAgaWYgKG1hdGNoZXMpIHtcbiAgICAgICAgbWF0Y2hlcy5mb3JFYWNoKG1hdGNoID0+IHtcbiAgICAgICAgICBjb25zdCBuYW1lcyA9IG1hdGNoLnNwbGl0KC9b77yMLOOAgV0vKS5tYXAobmFtZSA9PiBuYW1lLnRyaW0oKSk7XG4gICAgICAgICAgcGFydGljaXBhbnRzLnB1c2goLi4ubmFtZXMpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBwYXJ0aWNpcGFudHMubGVuZ3RoID4gMCA/IHBhcnRpY2lwYW50cy5zbGljZSgwLCA1KSA6IFsn5byg5LiJJywgJ+adjuWbmycsICfnjovkupQnXTtcbiAgfVxuXG4gIHByaXZhdGUgZXh0cmFjdERlY2lzaW9ucyh0ZXh0OiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgZGVjaXNpb25zID0gW107XG4gICAgY29uc3QgcGF0dGVybnMgPSBbXG4gICAgICAv5Yaz5a6aW++8mjpdKFte44CCXFxuXSspL2csXG4gICAgICAv56Gu5a6aW++8mjpdKFte44CCXFxuXSspL2csXG4gICAgICAv5ZCM5oSPW++8mjpdKFte44CCXFxuXSspL2dcbiAgICBdO1xuXG4gICAgcGF0dGVybnMuZm9yRWFjaChwYXR0ZXJuID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKHBhdHRlcm4pO1xuICAgICAgaWYgKG1hdGNoZXMpIHtcbiAgICAgICAgZGVjaXNpb25zLnB1c2goLi4ubWF0Y2hlcy5tYXAobWF0Y2ggPT4gbWF0Y2gucmVwbGFjZSgvXlte77yaOl0rW++8mjpdLywgJycpLnRyaW0oKSkpO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIGRlY2lzaW9ucy5sZW5ndGggPiAwID8gZGVjaXNpb25zIDogWyfnoa7lrprpobnnm67ml7bpl7TooagnLCAn5om55YeG6aKE566X5pa55qGIJywgJ+WQjOaEj+aKgOacr+mAieWeiyddO1xuICB9XG5cbiAgcHJpdmF0ZSBleHRyYWN0QWN0aW9ucyh0ZXh0OiBzdHJpbmcpOiBBcnJheTx7dGFzazogc3RyaW5nOyBhc3NpZ25lZTogc3RyaW5nOyBkZWFkbGluZT86IHN0cmluZzsgcHJpb3JpdHk6ICdoaWdoJyB8ICdtZWRpdW0nIHwgJ2xvdyd9PiB7XG4gICAgY29uc3QgYWN0aW9ucyA9IFtdO1xuICAgIGNvbnN0IGFjdGlvblBhdHRlcm5zID0gW1xuICAgICAgLyhb5byg5p2O546L6LW15YiY6ZmI5p2o6buE5ZGo5ZC0XVxcdyspW++8mjpdKFte44CCXFxuXSspL2csXG4gICAgICAv6LSf6LSj5Lq6W++8mjpdKFte77yMLOOAglxcbl0rKVvvvIwsXT8oW17jgIJcXG5dKikvZ1xuICAgIF07XG5cbiAgICBhY3Rpb25QYXR0ZXJucy5mb3JFYWNoKHBhdHRlcm4gPT4ge1xuICAgICAgY29uc3QgbWF0Y2hlcyA9IFsuLi50ZXh0Lm1hdGNoQWxsKHBhdHRlcm4pXTtcbiAgICAgIG1hdGNoZXMuZm9yRWFjaChtYXRjaCA9PiB7XG4gICAgICAgIGlmIChtYXRjaFsxXSAmJiBtYXRjaFsyXSkge1xuICAgICAgICAgIGFjdGlvbnMucHVzaCh7XG4gICAgICAgICAgICB0YXNrOiBtYXRjaFsyXS50cmltKCksXG4gICAgICAgICAgICBhc3NpZ25lZTogbWF0Y2hbMV0udHJpbSgpLFxuICAgICAgICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nIGFzIGNvbnN0XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgaWYgKGFjdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICBhY3Rpb25zLnB1c2goXG4gICAgICAgIHsgdGFzazogJ+WujOaIkOmhueebruaWh+aho+aVtOeQhicsIGFzc2lnbmVlOiAn5byg5LiJJywgcHJpb3JpdHk6ICdoaWdoJyBhcyBjb25zdCB9LFxuICAgICAgICB7IHRhc2s6ICflh4blpIfkuIvlkajmsYfmiqXmnZDmlpknLCBhc3NpZ25lZTogJ+adjuWbmycsIHByaW9yaXR5OiAnbWVkaXVtJyBhcyBjb25zdCB9LFxuICAgICAgICB7IHRhc2s6ICfot5/ov5vlrqLmiLflj43ppognLCBhc3NpZ25lZTogJ+eOi+S6lCcsIHByaW9yaXR5OiAnbG93JyBhcyBjb25zdCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBhY3Rpb25zO1xuICB9XG5cbiAgYXN5bmMgY2hlY2tDb25uZWN0aW9uKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIGlmICh0aGlzLmRlbW9Nb2RlKSByZXR1cm4gdHJ1ZTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vYXBpL3RhZ3NgKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5vaztcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBsaXN0TW9kZWxzKCk6IFByb21pc2U8c3RyaW5nW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2FwaS90YWdzYCk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSByZXR1cm4gW107XG4gICAgICBcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gZGF0YS5tb2RlbHM/Lm1hcCgobW9kZWw6IGFueSkgPT4gbW9kZWwubmFtZSkgfHwgW107XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiT2xsYW1hU2VydmljZSIsImdlbmVyYXRlTWVldGluZ01pbnV0ZXMiLCJtZWV0aW5nVGV4dCIsImRlbW9Nb2RlIiwiZ2VuZXJhdGVEZW1vTWludXRlcyIsInByb21wdCIsInJlc3BvbnNlIiwiZmV0Y2giLCJiYXNlVXJsIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibW9kZWwiLCJzdHJlYW0iLCJvayIsIkVycm9yIiwic3RhdHVzIiwiZGF0YSIsImpzb24iLCJjbGVhbmVkUmVzcG9uc2UiLCJ0cmltIiwianNvbk1hdGNoIiwibWF0Y2giLCJqc29uU3RyaW5nIiwibWVldGluZ01pbnV0ZXMiLCJwYXJzZSIsInRpdGxlIiwiZGF0ZSIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwicGFydGljaXBhbnRzIiwic3VtbWFyeSIsImtleURlY2lzaW9ucyIsImFjdGlvbkl0ZW1zIiwibmV4dFN0ZXBzIiwicGFyc2VFcnJvciIsImNvbnNvbGUiLCJlcnJvciIsImNyZWF0ZUZhbGxiYWNrTWludXRlcyIsInRhc2siLCJhc3NpZ25lZSIsInByaW9yaXR5IiwiZXh0cmFjdFBhcnRpY2lwYW50cyIsImRlY2lzaW9ucyIsImV4dHJhY3REZWNpc2lvbnMiLCJhY3Rpb25zIiwiZXh0cmFjdEFjdGlvbnMiLCJ0ZXh0IiwicGF0dGVybnMiLCJmb3JFYWNoIiwicGF0dGVybiIsIm1hdGNoZXMiLCJuYW1lcyIsIm1hcCIsIm5hbWUiLCJwdXNoIiwibGVuZ3RoIiwic2xpY2UiLCJyZXBsYWNlIiwiYWN0aW9uUGF0dGVybnMiLCJtYXRjaEFsbCIsImNoZWNrQ29ubmVjdGlvbiIsImxpc3RNb2RlbHMiLCJtb2RlbHMiLCJjb25zdHJ1Y3RvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});