"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaService: () => (/* binding */ OllamaService)\n/* harmony export */ });\n// Ollama API 集成\nclass OllamaService {\n    async generateMeetingMinutes(meetingText, onProgress) {\n        // 如果是演示模式，返回模拟数据\n        if (this.demoMode) {\n            return this.generateDemoMinutesWithProgress(meetingText, onProgress);\n        }\n        const prompt = \"\\n你是一个专业的会议纪要分析专家。请仔细分析以下会议内容，并生成结构化的会议纪要。\\n\\n请按照以下要求进行分析：\\n1. 仔细识别会议中的关键信息\\n2. 提取所有参与者姓名和职位\\n3. 总结会议的核心内容和目标\\n4. 识别所有明确的决策和决定\\n5. 提取所有行动项，包括负责人和时间要求\\n6. 分析任务的优先级（根据紧急程度和重要性）\\n\\n请以JSON格式返回结果，包含以下字段：\\n- title: 会议标题（如果没有明确标题，请根据内容生成一个合适的标题）\\n- date: 会议日期（格式：YYYY-MM-DD，如果没有明确日期，使用今天的日期）\\n- participants: 参与者列表（数组，包含姓名和职位信息）\\n- summary: 会议摘要（3-4句话，概括会议的主要内容和成果）\\n- keyDecisions: 关键决策列表（数组，每个决策用简洁明确的语言描述）\\n- actionItems: 行动项列表（数组，每个对象包含：task（具体任务描述）、assignee（负责人）、deadline（截止日期，可选）、priority（优先级：high/medium/low））\\n- nextSteps: 下一步计划（数组，列出后续需要进行的工作）\\n\\n会议内容：\\n\".concat(meetingText, \"\\n\\n请确保返回的是有效的JSON格式，不要包含markdown代码块标记或其他额外文本。JSON应该直接开始和结束。\\n\");\n        try {\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('正在连接 AI 服务...', '');\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/generate\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: this.model,\n                    prompt: prompt,\n                    stream: false\n                })\n            });\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress('AI 正在分析会议内容...', '');\n            if (!response.ok) {\n                throw new Error(\"Ollama API error: \".concat(response.status));\n            }\n            const data = await response.json();\n            // 尝试解析 JSON 响应\n            try {\n                const cleanedResponse = data.response.trim();\n                // 移除可能的 markdown 代码块标记\n                const jsonMatch = cleanedResponse.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/);\n                const jsonString = jsonMatch ? jsonMatch[1] : cleanedResponse;\n                const meetingMinutes = JSON.parse(jsonString);\n                // 验证必要字段\n                if (!meetingMinutes.title) meetingMinutes.title = '会议纪要';\n                if (!meetingMinutes.date) meetingMinutes.date = new Date().toISOString().split('T')[0];\n                if (!meetingMinutes.participants) meetingMinutes.participants = [];\n                if (!meetingMinutes.summary) meetingMinutes.summary = '会议摘要生成中...';\n                if (!meetingMinutes.keyDecisions) meetingMinutes.keyDecisions = [];\n                if (!meetingMinutes.actionItems) meetingMinutes.actionItems = [];\n                if (!meetingMinutes.nextSteps) meetingMinutes.nextSteps = [];\n                return meetingMinutes;\n            } catch (parseError) {\n                console.error('JSON parsing error:', parseError);\n                // 如果解析失败，返回基本结构\n                return this.createFallbackMinutes(meetingText);\n            }\n        } catch (error) {\n            console.error('Ollama service error:', error);\n            throw new Error('无法连接到 Ollama 服务。请确保 Ollama 正在运行并且模型已下载。');\n        }\n    }\n    createFallbackMinutes(meetingText) {\n        return {\n            title: '会议纪要',\n            date: new Date().toISOString().split('T')[0],\n            participants: [],\n            summary: '由于AI服务暂时不可用，请手动整理会议纪要。',\n            keyDecisions: [\n                '请手动添加关键决策'\n            ],\n            actionItems: [\n                {\n                    task: '请手动添加行动项',\n                    assignee: '待分配',\n                    priority: 'medium'\n                }\n            ],\n            nextSteps: [\n                '请手动添加下一步计划'\n            ]\n        };\n    }\n    async generateDemoMinutesWithProgress(meetingText, onProgress) {\n        const steps = [\n            {\n                step: '正在分析会议内容...',\n                delay: 800\n            },\n            {\n                step: '提取参与者信息...',\n                delay: 600\n            },\n            {\n                step: '识别关键决策...',\n                delay: 700\n            },\n            {\n                step: '分析行动项...',\n                delay: 900\n            },\n            {\n                step: '生成会议摘要...',\n                delay: 500\n            },\n            {\n                step: '整理最终结果...',\n                delay: 400\n            }\n        ];\n        for (const { step, delay } of steps){\n            onProgress === null || onProgress === void 0 ? void 0 : onProgress(step, '');\n            await new Promise((resolve)=>setTimeout(resolve, delay));\n        }\n        return this.generateDemoMinutes(meetingText);\n    }\n    generateDemoMinutes(meetingText) {\n        // 基于输入文本生成演示数据\n        const participants = this.extractParticipants(meetingText);\n        const decisions = this.extractDecisions(meetingText);\n        const actions = this.extractActions(meetingText);\n        return {\n            title: '会议纪要 (演示模式)',\n            date: new Date().toISOString().split('T')[0],\n            participants: participants,\n            summary: '这是演示模式生成的会议摘要。本次会议讨论了项目进展、关键决策和后续行动计划。',\n            keyDecisions: decisions,\n            actionItems: actions,\n            nextSteps: [\n                '继续推进项目开发',\n                '准备下次会议议程',\n                '跟进行动项执行情况'\n            ]\n        };\n    }\n    extractParticipants(text) {\n        const participants = [];\n        const patterns = [\n            /参与者[：:]\\s*([^。\\n]+)/g,\n            /与会人员[：:]\\s*([^。\\n]+)/g,\n            /([张李王赵刘陈杨黄周吴]\\w+)[（(]([^）)]+)[）)]/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                matches.forEach((match)=>{\n                    const names = match.split(/[，,、]/).map((name)=>name.trim());\n                    participants.push(...names);\n                });\n            }\n        });\n        return participants.length > 0 ? participants.slice(0, 5) : [\n            '张三',\n            '李四',\n            '王五'\n        ];\n    }\n    extractDecisions(text) {\n        const decisions = [];\n        const patterns = [\n            /决定[：:]([^。\\n]+)/g,\n            /确定[：:]([^。\\n]+)/g,\n            /同意[：:]([^。\\n]+)/g\n        ];\n        patterns.forEach((pattern)=>{\n            const matches = text.match(pattern);\n            if (matches) {\n                decisions.push(...matches.map((match)=>match.replace(/^[^：:]+[：:]/, '').trim()));\n            }\n        });\n        return decisions.length > 0 ? decisions : [\n            '确定项目时间表',\n            '批准预算方案',\n            '同意技术选型'\n        ];\n    }\n    extractActions(text) {\n        const actions = [];\n        const actionPatterns = [\n            /([张李王赵刘陈杨黄周吴]\\w+)[：:]([^。\\n]+)/g,\n            /负责人[：:]([^，,。\\n]+)[，,]?([^。\\n]*)/g\n        ];\n        actionPatterns.forEach((pattern)=>{\n            const matches = [\n                ...text.matchAll(pattern)\n            ];\n            matches.forEach((match)=>{\n                if (match[1] && match[2]) {\n                    actions.push({\n                        task: match[2].trim(),\n                        assignee: match[1].trim(),\n                        priority: 'medium'\n                    });\n                }\n            });\n        });\n        if (actions.length === 0) {\n            actions.push({\n                task: '完成项目文档整理',\n                assignee: '张三',\n                priority: 'high'\n            }, {\n                task: '准备下周汇报材料',\n                assignee: '李四',\n                priority: 'medium'\n            }, {\n                task: '跟进客户反馈',\n                assignee: '王五',\n                priority: 'low'\n            });\n        }\n        return actions;\n    }\n    async checkConnection() {\n        if (this.demoMode) return true;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            return response.ok;\n        } catch (e) {\n            return false;\n        }\n    }\n    async listModels() {\n        try {\n            var _data_models;\n            const response = await fetch(\"\".concat(this.baseUrl, \"/api/tags\"));\n            if (!response.ok) return [];\n            const data = await response.json();\n            return ((_data_models = data.models) === null || _data_models === void 0 ? void 0 : _data_models.map((model)=>model.name)) || [];\n        } catch (e) {\n            return [];\n        }\n    }\n    constructor(baseUrl = 'http://localhost:11434', model = 'qwq:32b', demoMode = false){\n        this.baseUrl = baseUrl;\n        this.model = model;\n        this.demoMode = demoMode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvb2xsYW1hLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxnQkFBZ0I7QUF1QlQsTUFBTUE7SUFXWCxNQUFNQyx1QkFDSkMsV0FBbUIsRUFDbkJDLFVBQW9ELEVBQzNCO1FBQ3pCLGlCQUFpQjtRQUNqQixJQUFJLElBQUksQ0FBQ0MsUUFBUSxFQUFFO1lBQ2pCLE9BQU8sSUFBSSxDQUFDQywrQkFBK0IsQ0FBQ0gsYUFBYUM7UUFDM0Q7UUFDQSxNQUFNRyxTQUFTLCtpQkFxQkwsT0FBWkosYUFBWTtRQUtWLElBQUk7WUFDRkMsdUJBQUFBLGlDQUFBQSxXQUFhLGlCQUFpQjtZQUU5QixNQUFNSSxXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYixJQUFJLENBQUNDLE9BQU8sRUFBQyxrQkFBZ0I7Z0JBQzNEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLE9BQU8sSUFBSSxDQUFDQSxLQUFLO29CQUNqQlQsUUFBUUE7b0JBQ1JVLFFBQVE7Z0JBQ1Y7WUFDRjtZQUVBYix1QkFBQUEsaUNBQUFBLFdBQWEsa0JBQWtCO1lBRS9CLElBQUksQ0FBQ0ksU0FBU1UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0scUJBQXFDLE9BQWhCWCxTQUFTWSxNQUFNO1lBQ3REO1lBRUEsTUFBTUMsT0FBdUIsTUFBTWIsU0FBU2MsSUFBSTtZQUVoRCxlQUFlO1lBQ2YsSUFBSTtnQkFDRixNQUFNQyxrQkFBa0JGLEtBQUtiLFFBQVEsQ0FBQ2dCLElBQUk7Z0JBQzFDLHVCQUF1QjtnQkFDdkIsTUFBTUMsWUFBWUYsZ0JBQWdCRyxLQUFLLENBQUM7Z0JBQ3hDLE1BQU1DLGFBQWFGLFlBQVlBLFNBQVMsQ0FBQyxFQUFFLEdBQUdGO2dCQUU5QyxNQUFNSyxpQkFBaUNkLEtBQUtlLEtBQUssQ0FBQ0Y7Z0JBRWxELFNBQVM7Z0JBQ1QsSUFBSSxDQUFDQyxlQUFlRSxLQUFLLEVBQUVGLGVBQWVFLEtBQUssR0FBRztnQkFDbEQsSUFBSSxDQUFDRixlQUFlRyxJQUFJLEVBQUVILGVBQWVHLElBQUksR0FBRyxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDdEYsSUFBSSxDQUFDTixlQUFlTyxZQUFZLEVBQUVQLGVBQWVPLFlBQVksR0FBRyxFQUFFO2dCQUNsRSxJQUFJLENBQUNQLGVBQWVRLE9BQU8sRUFBRVIsZUFBZVEsT0FBTyxHQUFHO2dCQUN0RCxJQUFJLENBQUNSLGVBQWVTLFlBQVksRUFBRVQsZUFBZVMsWUFBWSxHQUFHLEVBQUU7Z0JBQ2xFLElBQUksQ0FBQ1QsZUFBZVUsV0FBVyxFQUFFVixlQUFlVSxXQUFXLEdBQUcsRUFBRTtnQkFDaEUsSUFBSSxDQUFDVixlQUFlVyxTQUFTLEVBQUVYLGVBQWVXLFNBQVMsR0FBRyxFQUFFO2dCQUU1RCxPQUFPWDtZQUNULEVBQUUsT0FBT1ksWUFBWTtnQkFDbkJDLFFBQVFDLEtBQUssQ0FBQyx1QkFBdUJGO2dCQUNyQyxnQkFBZ0I7Z0JBQ2hCLE9BQU8sSUFBSSxDQUFDRyxxQkFBcUIsQ0FBQ3hDO1lBQ3BDO1FBQ0YsRUFBRSxPQUFPdUMsT0FBTztZQUNkRCxRQUFRQyxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNLElBQUl2QixNQUFNO1FBQ2xCO0lBQ0Y7SUFFUXdCLHNCQUFzQnhDLFdBQW1CLEVBQWtCO1FBQ2pFLE9BQU87WUFDTDJCLE9BQU87WUFDUEMsTUFBTSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM1Q0MsY0FBYyxFQUFFO1lBQ2hCQyxTQUFTO1lBQ1RDLGNBQWM7Z0JBQUM7YUFBWTtZQUMzQkMsYUFBYTtnQkFBQztvQkFDWk0sTUFBTTtvQkFDTkMsVUFBVTtvQkFDVkMsVUFBVTtnQkFDWjthQUFFO1lBQ0ZQLFdBQVc7Z0JBQUM7YUFBYTtRQUMzQjtJQUNGO0lBRUEsTUFBY2pDLGdDQUNaSCxXQUFtQixFQUNuQkMsVUFBb0QsRUFDM0I7UUFDekIsTUFBTTJDLFFBQVE7WUFDWjtnQkFBRUMsTUFBTTtnQkFBZUMsT0FBTztZQUFJO1lBQ2xDO2dCQUFFRCxNQUFNO2dCQUFjQyxPQUFPO1lBQUk7WUFDakM7Z0JBQUVELE1BQU07Z0JBQWFDLE9BQU87WUFBSTtZQUNoQztnQkFBRUQsTUFBTTtnQkFBWUMsT0FBTztZQUFJO1lBQy9CO2dCQUFFRCxNQUFNO2dCQUFhQyxPQUFPO1lBQUk7WUFDaEM7Z0JBQUVELE1BQU07Z0JBQWFDLE9BQU87WUFBSTtTQUNqQztRQUVELEtBQUssTUFBTSxFQUFFRCxJQUFJLEVBQUVDLEtBQUssRUFBRSxJQUFJRixNQUFPO1lBQ25DM0MsdUJBQUFBLGlDQUFBQSxXQUFhNEMsTUFBTTtZQUNuQixNQUFNLElBQUlFLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVNGO1FBQ25EO1FBRUEsT0FBTyxJQUFJLENBQUNJLG1CQUFtQixDQUFDbEQ7SUFDbEM7SUFFUWtELG9CQUFvQmxELFdBQW1CLEVBQWtCO1FBQy9ELGVBQWU7UUFDZixNQUFNZ0MsZUFBZSxJQUFJLENBQUNtQixtQkFBbUIsQ0FBQ25EO1FBQzlDLE1BQU1vRCxZQUFZLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUNyRDtRQUN4QyxNQUFNc0QsVUFBVSxJQUFJLENBQUNDLGNBQWMsQ0FBQ3ZEO1FBRXBDLE9BQU87WUFDTDJCLE9BQU87WUFDUEMsTUFBTSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM1Q0MsY0FBY0E7WUFDZEMsU0FBUztZQUNUQyxjQUFja0I7WUFDZGpCLGFBQWFtQjtZQUNibEIsV0FBVztnQkFBQztnQkFBWTtnQkFBWTthQUFZO1FBQ2xEO0lBQ0Y7SUFFUWUsb0JBQW9CSyxJQUFZLEVBQVk7UUFDbEQsTUFBTXhCLGVBQWUsRUFBRTtRQUN2QixNQUFNeUIsV0FBVztZQUNmO1lBQ0E7WUFDQTtTQUNEO1FBRURBLFNBQVNDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDZixNQUFNQyxVQUFVSixLQUFLakMsS0FBSyxDQUFDb0M7WUFDM0IsSUFBSUMsU0FBUztnQkFDWEEsUUFBUUYsT0FBTyxDQUFDbkMsQ0FBQUE7b0JBQ2QsTUFBTXNDLFFBQVF0QyxNQUFNUSxLQUFLLENBQUMsU0FBUytCLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBSzFDLElBQUk7b0JBQ3hEVyxhQUFhZ0MsSUFBSSxJQUFJSDtnQkFDdkI7WUFDRjtRQUNGO1FBRUEsT0FBTzdCLGFBQWFpQyxNQUFNLEdBQUcsSUFBSWpDLGFBQWFrQyxLQUFLLENBQUMsR0FBRyxLQUFLO1lBQUM7WUFBTTtZQUFNO1NBQUs7SUFDaEY7SUFFUWIsaUJBQWlCRyxJQUFZLEVBQVk7UUFDL0MsTUFBTUosWUFBWSxFQUFFO1FBQ3BCLE1BQU1LLFdBQVc7WUFDZjtZQUNBO1lBQ0E7U0FDRDtRQUVEQSxTQUFTQyxPQUFPLENBQUNDLENBQUFBO1lBQ2YsTUFBTUMsVUFBVUosS0FBS2pDLEtBQUssQ0FBQ29DO1lBQzNCLElBQUlDLFNBQVM7Z0JBQ1hSLFVBQVVZLElBQUksSUFBSUosUUFBUUUsR0FBRyxDQUFDdkMsQ0FBQUEsUUFBU0EsTUFBTTRDLE9BQU8sQ0FBQyxlQUFlLElBQUk5QyxJQUFJO1lBQzlFO1FBQ0Y7UUFFQSxPQUFPK0IsVUFBVWEsTUFBTSxHQUFHLElBQUliLFlBQVk7WUFBQztZQUFXO1lBQVU7U0FBUztJQUMzRTtJQUVRRyxlQUFlQyxJQUFZLEVBQW1HO1FBQ3BJLE1BQU1GLFVBQVUsRUFBRTtRQUNsQixNQUFNYyxpQkFBaUI7WUFDckI7WUFDQTtTQUNEO1FBRURBLGVBQWVWLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDckIsTUFBTUMsVUFBVTttQkFBSUosS0FBS2EsUUFBUSxDQUFDVjthQUFTO1lBQzNDQyxRQUFRRixPQUFPLENBQUNuQyxDQUFBQTtnQkFDZCxJQUFJQSxLQUFLLENBQUMsRUFBRSxJQUFJQSxLQUFLLENBQUMsRUFBRSxFQUFFO29CQUN4QitCLFFBQVFVLElBQUksQ0FBQzt3QkFDWHZCLE1BQU1sQixLQUFLLENBQUMsRUFBRSxDQUFDRixJQUFJO3dCQUNuQnFCLFVBQVVuQixLQUFLLENBQUMsRUFBRSxDQUFDRixJQUFJO3dCQUN2QnNCLFVBQVU7b0JBQ1o7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSVcsUUFBUVcsTUFBTSxLQUFLLEdBQUc7WUFDeEJYLFFBQVFVLElBQUksQ0FDVjtnQkFBRXZCLE1BQU07Z0JBQVlDLFVBQVU7Z0JBQU1DLFVBQVU7WUFBZ0IsR0FDOUQ7Z0JBQUVGLE1BQU07Z0JBQVlDLFVBQVU7Z0JBQU1DLFVBQVU7WUFBa0IsR0FDaEU7Z0JBQUVGLE1BQU07Z0JBQVVDLFVBQVU7Z0JBQU1DLFVBQVU7WUFBZTtRQUUvRDtRQUVBLE9BQU9XO0lBQ1Q7SUFFQSxNQUFNZ0Isa0JBQW9DO1FBQ3hDLElBQUksSUFBSSxDQUFDcEUsUUFBUSxFQUFFLE9BQU87UUFFMUIsSUFBSTtZQUNGLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFiLElBQUksQ0FBQ0MsT0FBTyxFQUFDO1lBQzdDLE9BQU9GLFNBQVNVLEVBQUU7UUFDcEIsRUFBRSxVQUFNO1lBQ04sT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNd0QsYUFBZ0M7UUFDcEMsSUFBSTtnQkFLS3JEO1lBSlAsTUFBTWIsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDQyxPQUFPLEVBQUM7WUFDN0MsSUFBSSxDQUFDRixTQUFTVSxFQUFFLEVBQUUsT0FBTyxFQUFFO1lBRTNCLE1BQU1HLE9BQU8sTUFBTWIsU0FBU2MsSUFBSTtZQUNoQyxPQUFPRCxFQUFBQSxlQUFBQSxLQUFLc0QsTUFBTSxjQUFYdEQsbUNBQUFBLGFBQWE0QyxHQUFHLENBQUMsQ0FBQ2pELFFBQWVBLE1BQU1rRCxJQUFJLE1BQUssRUFBRTtRQUMzRCxFQUFFLFVBQU07WUFDTixPQUFPLEVBQUU7UUFDWDtJQUNGO0lBOU9BVSxZQUFZbEUsVUFBVSx3QkFBd0IsRUFBRU0sUUFBUSxTQUFTLEVBQUVYLFdBQVcsS0FBSyxDQUFFO1FBQ25GLElBQUksQ0FBQ0ssT0FBTyxHQUFHQTtRQUNmLElBQUksQ0FBQ00sS0FBSyxHQUFHQTtRQUNiLElBQUksQ0FBQ1gsUUFBUSxHQUFHQTtJQUNsQjtBQTJPRiIsInNvdXJjZXMiOlsiRDpcXHByXFxodG1sXFxzcmNcXGxpYlxcb2xsYW1hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE9sbGFtYSBBUEkg6ZuG5oiQXG5leHBvcnQgaW50ZXJmYWNlIE9sbGFtYVJlc3BvbnNlIHtcbiAgbW9kZWw6IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICByZXNwb25zZTogc3RyaW5nO1xuICBkb25lOiBib29sZWFuO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIE1lZXRpbmdNaW51dGVzIHtcbiAgdGl0bGU6IHN0cmluZztcbiAgZGF0ZTogc3RyaW5nO1xuICBwYXJ0aWNpcGFudHM6IHN0cmluZ1tdO1xuICBzdW1tYXJ5OiBzdHJpbmc7XG4gIGtleURlY2lzaW9uczogc3RyaW5nW107XG4gIGFjdGlvbkl0ZW1zOiBBcnJheTx7XG4gICAgdGFzazogc3RyaW5nO1xuICAgIGFzc2lnbmVlOiBzdHJpbmc7XG4gICAgZGVhZGxpbmU/OiBzdHJpbmc7XG4gICAgcHJpb3JpdHk6ICdoaWdoJyB8ICdtZWRpdW0nIHwgJ2xvdyc7XG4gIH0+O1xuICBuZXh0U3RlcHM6IHN0cmluZ1tdO1xufVxuXG5leHBvcnQgY2xhc3MgT2xsYW1hU2VydmljZSB7XG4gIHByaXZhdGUgYmFzZVVybDogc3RyaW5nO1xuICBwcml2YXRlIG1vZGVsOiBzdHJpbmc7XG4gIHByaXZhdGUgZGVtb01vZGU6IGJvb2xlYW47XG5cbiAgY29uc3RydWN0b3IoYmFzZVVybCA9ICdodHRwOi8vbG9jYWxob3N0OjExNDM0JywgbW9kZWwgPSAncXdxOjMyYicsIGRlbW9Nb2RlID0gZmFsc2UpIHtcbiAgICB0aGlzLmJhc2VVcmwgPSBiYXNlVXJsO1xuICAgIHRoaXMubW9kZWwgPSBtb2RlbDtcbiAgICB0aGlzLmRlbW9Nb2RlID0gZGVtb01vZGU7XG4gIH1cblxuICBhc3luYyBnZW5lcmF0ZU1lZXRpbmdNaW51dGVzKFxuICAgIG1lZXRpbmdUZXh0OiBzdHJpbmcsXG4gICAgb25Qcm9ncmVzcz86IChzdGVwOiBzdHJpbmcsIGNvbnRlbnQ6IHN0cmluZykgPT4gdm9pZFxuICApOiBQcm9taXNlPE1lZXRpbmdNaW51dGVzPiB7XG4gICAgLy8g5aaC5p6c5piv5ryU56S65qih5byP77yM6L+U5Zue5qih5ouf5pWw5o2uXG4gICAgaWYgKHRoaXMuZGVtb01vZGUpIHtcbiAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlRGVtb01pbnV0ZXNXaXRoUHJvZ3Jlc3MobWVldGluZ1RleHQsIG9uUHJvZ3Jlc3MpO1xuICAgIH1cbiAgICBjb25zdCBwcm9tcHQgPSBgXG7kvaDmmK/kuIDkuKrkuJPkuJrnmoTkvJrorq7nuqropoHliIbmnpDkuJPlrrbjgILor7fku5Tnu4bliIbmnpDku6XkuIvkvJrorq7lhoXlrrnvvIzlubbnlJ/miJDnu5PmnoTljJbnmoTkvJrorq7nuqropoHjgIJcblxu6K+35oyJ54Wn5Lul5LiL6KaB5rGC6L+b6KGM5YiG5p6Q77yaXG4xLiDku5Tnu4bor4bliKvkvJrorq7kuK3nmoTlhbPplK7kv6Hmga9cbjIuIOaPkOWPluaJgOacieWPguS4juiAheWnk+WQjeWSjOiBjOS9jVxuMy4g5oC757uT5Lya6K6u55qE5qC45b+D5YaF5a655ZKM55uu5qCHXG40LiDor4bliKvmiYDmnInmmI7noa7nmoTlhrPnrZblkozlhrPlrppcbjUuIOaPkOWPluaJgOacieihjOWKqOmhue+8jOWMheaLrOi0n+i0o+S6uuWSjOaXtumXtOimgeaxglxuNi4g5YiG5p6Q5Lu75Yqh55qE5LyY5YWI57qn77yI5qC55o2u57Sn5oCl56iL5bqm5ZKM6YeN6KaB5oCn77yJXG5cbuivt+S7pUpTT07moLzlvI/ov5Tlm57nu5PmnpzvvIzljIXlkKvku6XkuIvlrZfmrrXvvJpcbi0gdGl0bGU6IOS8muiuruagh+mimO+8iOWmguaenOayoeacieaYjuehruagh+mimO+8jOivt+agueaNruWGheWuueeUn+aIkOS4gOS4quWQiOmAgueahOagh+mimO+8iVxuLSBkYXRlOiDkvJrorq7ml6XmnJ/vvIjmoLzlvI/vvJpZWVlZLU1NLURE77yM5aaC5p6c5rKh5pyJ5piO56Gu5pel5pyf77yM5L2/55So5LuK5aSp55qE5pel5pyf77yJXG4tIHBhcnRpY2lwYW50czog5Y+C5LiO6ICF5YiX6KGo77yI5pWw57uE77yM5YyF5ZCr5aeT5ZCN5ZKM6IGM5L2N5L+h5oGv77yJXG4tIHN1bW1hcnk6IOS8muiuruaRmOimge+8iDMtNOWPpeivne+8jOamguaLrOS8muiurueahOS4u+imgeWGheWuueWSjOaIkOaenO+8iVxuLSBrZXlEZWNpc2lvbnM6IOWFs+mUruWGs+etluWIl+ihqO+8iOaVsOe7hO+8jOavj+S4quWGs+etlueUqOeugOa0geaYjuehrueahOivreiogOaPj+i/sO+8iVxuLSBhY3Rpb25JdGVtczog6KGM5Yqo6aG55YiX6KGo77yI5pWw57uE77yM5q+P5Liq5a+56LGh5YyF5ZCr77yadGFza++8iOWFt+S9k+S7u+WKoeaPj+i/sO+8ieOAgWFzc2lnbmVl77yI6LSf6LSj5Lq677yJ44CBZGVhZGxpbmXvvIjmiKrmraLml6XmnJ/vvIzlj6/pgInvvInjgIFwcmlvcml0ee+8iOS8mOWFiOe6p++8mmhpZ2gvbWVkaXVtL2xvd++8ie+8iVxuLSBuZXh0U3RlcHM6IOS4i+S4gOatpeiuoeWIku+8iOaVsOe7hO+8jOWIl+WHuuWQjue7remcgOimgei/m+ihjOeahOW3peS9nO+8iVxuXG7kvJrorq7lhoXlrrnvvJpcbiR7bWVldGluZ1RleHR9XG5cbuivt+ehruS/nei/lOWbnueahOaYr+acieaViOeahEpTT07moLzlvI/vvIzkuI3opoHljIXlkKttYXJrZG93buS7o+eggeWdl+agh+iusOaIluWFtuS7lumineWkluaWh+acrOOAgkpTT07lupTor6Xnm7TmjqXlvIDlp4vlkoznu5PmnZ/jgIJcbmA7XG5cbiAgICB0cnkge1xuICAgICAgb25Qcm9ncmVzcz8uKCfmraPlnKjov57mjqUgQUkg5pyN5YqhLi4uJywgJycpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vYXBpL2dlbmVyYXRlYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBtb2RlbDogdGhpcy5tb2RlbCxcbiAgICAgICAgICBwcm9tcHQ6IHByb21wdCxcbiAgICAgICAgICBzdHJlYW06IGZhbHNlLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBvblByb2dyZXNzPy4oJ0FJIOato+WcqOWIhuaekOS8muiuruWGheWuuS4uLicsICcnKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYE9sbGFtYSBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhOiBPbGxhbWFSZXNwb25zZSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIFxuICAgICAgLy8g5bCd6K+V6Kej5p6QIEpTT04g5ZON5bqUXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjbGVhbmVkUmVzcG9uc2UgPSBkYXRhLnJlc3BvbnNlLnRyaW0oKTtcbiAgICAgICAgLy8g56e76Zmk5Y+v6IO955qEIG1hcmtkb3duIOS7o+eggeWdl+agh+iusFxuICAgICAgICBjb25zdCBqc29uTWF0Y2ggPSBjbGVhbmVkUmVzcG9uc2UubWF0Y2goL2BgYCg/Ompzb24pP1xccyooW1xcc1xcU10qPylcXHMqYGBgLyk7XG4gICAgICAgIGNvbnN0IGpzb25TdHJpbmcgPSBqc29uTWF0Y2ggPyBqc29uTWF0Y2hbMV0gOiBjbGVhbmVkUmVzcG9uc2U7XG4gICAgICAgIFxuICAgICAgICBjb25zdCBtZWV0aW5nTWludXRlczogTWVldGluZ01pbnV0ZXMgPSBKU09OLnBhcnNlKGpzb25TdHJpbmcpO1xuICAgICAgICBcbiAgICAgICAgLy8g6aqM6K+B5b+F6KaB5a2X5q61XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMudGl0bGUpIG1lZXRpbmdNaW51dGVzLnRpdGxlID0gJ+S8muiurue6quimgSc7XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMuZGF0ZSkgbWVldGluZ01pbnV0ZXMuZGF0ZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLnBhcnRpY2lwYW50cykgbWVldGluZ01pbnV0ZXMucGFydGljaXBhbnRzID0gW107XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMuc3VtbWFyeSkgbWVldGluZ01pbnV0ZXMuc3VtbWFyeSA9ICfkvJrorq7mkZjopoHnlJ/miJDkuK0uLi4nO1xuICAgICAgICBpZiAoIW1lZXRpbmdNaW51dGVzLmtleURlY2lzaW9ucykgbWVldGluZ01pbnV0ZXMua2V5RGVjaXNpb25zID0gW107XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMuYWN0aW9uSXRlbXMpIG1lZXRpbmdNaW51dGVzLmFjdGlvbkl0ZW1zID0gW107XG4gICAgICAgIGlmICghbWVldGluZ01pbnV0ZXMubmV4dFN0ZXBzKSBtZWV0aW5nTWludXRlcy5uZXh0U3RlcHMgPSBbXTtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBtZWV0aW5nTWludXRlcztcbiAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignSlNPTiBwYXJzaW5nIGVycm9yOicsIHBhcnNlRXJyb3IpO1xuICAgICAgICAvLyDlpoLmnpzop6PmnpDlpLHotKXvvIzov5Tlm57ln7rmnKznu5PmnoRcbiAgICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlRmFsbGJhY2tNaW51dGVzKG1lZXRpbmdUZXh0KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignT2xsYW1hIHNlcnZpY2UgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5Xov57mjqXliLAgT2xsYW1hIOacjeWKoeOAguivt+ehruS/nSBPbGxhbWEg5q2j5Zyo6L+Q6KGM5bm25LiU5qih5Z6L5bey5LiL6L2944CCJyk7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBjcmVhdGVGYWxsYmFja01pbnV0ZXMobWVldGluZ1RleHQ6IHN0cmluZyk6IE1lZXRpbmdNaW51dGVzIHtcbiAgICByZXR1cm4ge1xuICAgICAgdGl0bGU6ICfkvJrorq7nuqropoEnLFxuICAgICAgZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICBwYXJ0aWNpcGFudHM6IFtdLFxuICAgICAgc3VtbWFyeTogJ+eUseS6jkFJ5pyN5Yqh5pqC5pe25LiN5Y+v55So77yM6K+35omL5Yqo5pW055CG5Lya6K6u57qq6KaB44CCJyxcbiAgICAgIGtleURlY2lzaW9uczogWyfor7fmiYvliqjmt7vliqDlhbPplK7lhrPnrZYnXSxcbiAgICAgIGFjdGlvbkl0ZW1zOiBbe1xuICAgICAgICB0YXNrOiAn6K+35omL5Yqo5re75Yqg6KGM5Yqo6aG5JyxcbiAgICAgICAgYXNzaWduZWU6ICflvoXliIbphY0nLFxuICAgICAgICBwcmlvcml0eTogJ21lZGl1bScgYXMgY29uc3RcbiAgICAgIH1dLFxuICAgICAgbmV4dFN0ZXBzOiBbJ+ivt+aJi+WKqOa3u+WKoOS4i+S4gOatpeiuoeWIkiddXG4gICAgfTtcbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgZ2VuZXJhdGVEZW1vTWludXRlc1dpdGhQcm9ncmVzcyhcbiAgICBtZWV0aW5nVGV4dDogc3RyaW5nLFxuICAgIG9uUHJvZ3Jlc3M/OiAoc3RlcDogc3RyaW5nLCBjb250ZW50OiBzdHJpbmcpID0+IHZvaWRcbiAgKTogUHJvbWlzZTxNZWV0aW5nTWludXRlcz4ge1xuICAgIGNvbnN0IHN0ZXBzID0gW1xuICAgICAgeyBzdGVwOiAn5q2j5Zyo5YiG5p6Q5Lya6K6u5YaF5a65Li4uJywgZGVsYXk6IDgwMCB9LFxuICAgICAgeyBzdGVwOiAn5o+Q5Y+W5Y+C5LiO6ICF5L+h5oGvLi4uJywgZGVsYXk6IDYwMCB9LFxuICAgICAgeyBzdGVwOiAn6K+G5Yir5YWz6ZSu5Yaz562WLi4uJywgZGVsYXk6IDcwMCB9LFxuICAgICAgeyBzdGVwOiAn5YiG5p6Q6KGM5Yqo6aG5Li4uJywgZGVsYXk6IDkwMCB9LFxuICAgICAgeyBzdGVwOiAn55Sf5oiQ5Lya6K6u5pGY6KaBLi4uJywgZGVsYXk6IDUwMCB9LFxuICAgICAgeyBzdGVwOiAn5pW055CG5pyA57uI57uT5p6cLi4uJywgZGVsYXk6IDQwMCB9XG4gICAgXTtcblxuICAgIGZvciAoY29uc3QgeyBzdGVwLCBkZWxheSB9IG9mIHN0ZXBzKSB7XG4gICAgICBvblByb2dyZXNzPy4oc3RlcCwgJycpO1xuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIGRlbGF5KSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHRoaXMuZ2VuZXJhdGVEZW1vTWludXRlcyhtZWV0aW5nVGV4dCk7XG4gIH1cblxuICBwcml2YXRlIGdlbmVyYXRlRGVtb01pbnV0ZXMobWVldGluZ1RleHQ6IHN0cmluZyk6IE1lZXRpbmdNaW51dGVzIHtcbiAgICAvLyDln7rkuo7ovpPlhaXmlofmnKznlJ/miJDmvJTnpLrmlbDmja5cbiAgICBjb25zdCBwYXJ0aWNpcGFudHMgPSB0aGlzLmV4dHJhY3RQYXJ0aWNpcGFudHMobWVldGluZ1RleHQpO1xuICAgIGNvbnN0IGRlY2lzaW9ucyA9IHRoaXMuZXh0cmFjdERlY2lzaW9ucyhtZWV0aW5nVGV4dCk7XG4gICAgY29uc3QgYWN0aW9ucyA9IHRoaXMuZXh0cmFjdEFjdGlvbnMobWVldGluZ1RleHQpO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHRpdGxlOiAn5Lya6K6u57qq6KaBICjmvJTnpLrmqKHlvI8pJyxcbiAgICAgIGRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgcGFydGljaXBhbnRzOiBwYXJ0aWNpcGFudHMsXG4gICAgICBzdW1tYXJ5OiAn6L+Z5piv5ryU56S65qih5byP55Sf5oiQ55qE5Lya6K6u5pGY6KaB44CC5pys5qyh5Lya6K6u6K6o6K665LqG6aG555uu6L+b5bGV44CB5YWz6ZSu5Yaz562W5ZKM5ZCO57ut6KGM5Yqo6K6h5YiS44CCJyxcbiAgICAgIGtleURlY2lzaW9uczogZGVjaXNpb25zLFxuICAgICAgYWN0aW9uSXRlbXM6IGFjdGlvbnMsXG4gICAgICBuZXh0U3RlcHM6IFsn57un57ut5o6o6L+b6aG555uu5byA5Y+RJywgJ+WHhuWkh+S4i+asoeS8muiuruiurueoiycsICfot5/ov5vooYzliqjpobnmiafooYzmg4XlhrUnXVxuICAgIH07XG4gIH1cblxuICBwcml2YXRlIGV4dHJhY3RQYXJ0aWNpcGFudHModGV4dDogc3RyaW5nKTogc3RyaW5nW10ge1xuICAgIGNvbnN0IHBhcnRpY2lwYW50cyA9IFtdO1xuICAgIGNvbnN0IHBhdHRlcm5zID0gW1xuICAgICAgL+WPguS4juiAhVvvvJo6XVxccyooW17jgIJcXG5dKykvZyxcbiAgICAgIC/kuI7kvJrkurrlkZhb77yaOl1cXHMqKFte44CCXFxuXSspL2csXG4gICAgICAvKFvlvKDmnY7njovotbXliJjpmYjmnajpu4TlkajlkLRdXFx3Kylb77yIKF0oW17vvIkpXSspW++8iSldL2dcbiAgICBdO1xuXG4gICAgcGF0dGVybnMuZm9yRWFjaChwYXR0ZXJuID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKHBhdHRlcm4pO1xuICAgICAgaWYgKG1hdGNoZXMpIHtcbiAgICAgICAgbWF0Y2hlcy5mb3JFYWNoKG1hdGNoID0+IHtcbiAgICAgICAgICBjb25zdCBuYW1lcyA9IG1hdGNoLnNwbGl0KC9b77yMLOOAgV0vKS5tYXAobmFtZSA9PiBuYW1lLnRyaW0oKSk7XG4gICAgICAgICAgcGFydGljaXBhbnRzLnB1c2goLi4ubmFtZXMpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBwYXJ0aWNpcGFudHMubGVuZ3RoID4gMCA/IHBhcnRpY2lwYW50cy5zbGljZSgwLCA1KSA6IFsn5byg5LiJJywgJ+adjuWbmycsICfnjovkupQnXTtcbiAgfVxuXG4gIHByaXZhdGUgZXh0cmFjdERlY2lzaW9ucyh0ZXh0OiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgZGVjaXNpb25zID0gW107XG4gICAgY29uc3QgcGF0dGVybnMgPSBbXG4gICAgICAv5Yaz5a6aW++8mjpdKFte44CCXFxuXSspL2csXG4gICAgICAv56Gu5a6aW++8mjpdKFte44CCXFxuXSspL2csXG4gICAgICAv5ZCM5oSPW++8mjpdKFte44CCXFxuXSspL2dcbiAgICBdO1xuXG4gICAgcGF0dGVybnMuZm9yRWFjaChwYXR0ZXJuID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKHBhdHRlcm4pO1xuICAgICAgaWYgKG1hdGNoZXMpIHtcbiAgICAgICAgZGVjaXNpb25zLnB1c2goLi4ubWF0Y2hlcy5tYXAobWF0Y2ggPT4gbWF0Y2gucmVwbGFjZSgvXlte77yaOl0rW++8mjpdLywgJycpLnRyaW0oKSkpO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIGRlY2lzaW9ucy5sZW5ndGggPiAwID8gZGVjaXNpb25zIDogWyfnoa7lrprpobnnm67ml7bpl7TooagnLCAn5om55YeG6aKE566X5pa55qGIJywgJ+WQjOaEj+aKgOacr+mAieWeiyddO1xuICB9XG5cbiAgcHJpdmF0ZSBleHRyYWN0QWN0aW9ucyh0ZXh0OiBzdHJpbmcpOiBBcnJheTx7dGFzazogc3RyaW5nOyBhc3NpZ25lZTogc3RyaW5nOyBkZWFkbGluZT86IHN0cmluZzsgcHJpb3JpdHk6ICdoaWdoJyB8ICdtZWRpdW0nIHwgJ2xvdyd9PiB7XG4gICAgY29uc3QgYWN0aW9ucyA9IFtdO1xuICAgIGNvbnN0IGFjdGlvblBhdHRlcm5zID0gW1xuICAgICAgLyhb5byg5p2O546L6LW15YiY6ZmI5p2o6buE5ZGo5ZC0XVxcdyspW++8mjpdKFte44CCXFxuXSspL2csXG4gICAgICAv6LSf6LSj5Lq6W++8mjpdKFte77yMLOOAglxcbl0rKVvvvIwsXT8oW17jgIJcXG5dKikvZ1xuICAgIF07XG5cbiAgICBhY3Rpb25QYXR0ZXJucy5mb3JFYWNoKHBhdHRlcm4gPT4ge1xuICAgICAgY29uc3QgbWF0Y2hlcyA9IFsuLi50ZXh0Lm1hdGNoQWxsKHBhdHRlcm4pXTtcbiAgICAgIG1hdGNoZXMuZm9yRWFjaChtYXRjaCA9PiB7XG4gICAgICAgIGlmIChtYXRjaFsxXSAmJiBtYXRjaFsyXSkge1xuICAgICAgICAgIGFjdGlvbnMucHVzaCh7XG4gICAgICAgICAgICB0YXNrOiBtYXRjaFsyXS50cmltKCksXG4gICAgICAgICAgICBhc3NpZ25lZTogbWF0Y2hbMV0udHJpbSgpLFxuICAgICAgICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nIGFzIGNvbnN0XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgaWYgKGFjdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICBhY3Rpb25zLnB1c2goXG4gICAgICAgIHsgdGFzazogJ+WujOaIkOmhueebruaWh+aho+aVtOeQhicsIGFzc2lnbmVlOiAn5byg5LiJJywgcHJpb3JpdHk6ICdoaWdoJyBhcyBjb25zdCB9LFxuICAgICAgICB7IHRhc2s6ICflh4blpIfkuIvlkajmsYfmiqXmnZDmlpknLCBhc3NpZ25lZTogJ+adjuWbmycsIHByaW9yaXR5OiAnbWVkaXVtJyBhcyBjb25zdCB9LFxuICAgICAgICB7IHRhc2s6ICfot5/ov5vlrqLmiLflj43ppognLCBhc3NpZ25lZTogJ+eOi+S6lCcsIHByaW9yaXR5OiAnbG93JyBhcyBjb25zdCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBhY3Rpb25zO1xuICB9XG5cbiAgYXN5bmMgY2hlY2tDb25uZWN0aW9uKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIGlmICh0aGlzLmRlbW9Nb2RlKSByZXR1cm4gdHJ1ZTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vYXBpL3RhZ3NgKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5vaztcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBsaXN0TW9kZWxzKCk6IFByb21pc2U8c3RyaW5nW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2FwaS90YWdzYCk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSByZXR1cm4gW107XG4gICAgICBcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gZGF0YS5tb2RlbHM/Lm1hcCgobW9kZWw6IGFueSkgPT4gbW9kZWwubmFtZSkgfHwgW107XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiT2xsYW1hU2VydmljZSIsImdlbmVyYXRlTWVldGluZ01pbnV0ZXMiLCJtZWV0aW5nVGV4dCIsIm9uUHJvZ3Jlc3MiLCJkZW1vTW9kZSIsImdlbmVyYXRlRGVtb01pbnV0ZXNXaXRoUHJvZ3Jlc3MiLCJwcm9tcHQiLCJyZXNwb25zZSIsImZldGNoIiwiYmFzZVVybCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1vZGVsIiwic3RyZWFtIiwib2siLCJFcnJvciIsInN0YXR1cyIsImRhdGEiLCJqc29uIiwiY2xlYW5lZFJlc3BvbnNlIiwidHJpbSIsImpzb25NYXRjaCIsIm1hdGNoIiwianNvblN0cmluZyIsIm1lZXRpbmdNaW51dGVzIiwicGFyc2UiLCJ0aXRsZSIsImRhdGUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInBhcnRpY2lwYW50cyIsInN1bW1hcnkiLCJrZXlEZWNpc2lvbnMiLCJhY3Rpb25JdGVtcyIsIm5leHRTdGVwcyIsInBhcnNlRXJyb3IiLCJjb25zb2xlIiwiZXJyb3IiLCJjcmVhdGVGYWxsYmFja01pbnV0ZXMiLCJ0YXNrIiwiYXNzaWduZWUiLCJwcmlvcml0eSIsInN0ZXBzIiwic3RlcCIsImRlbGF5IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZ2VuZXJhdGVEZW1vTWludXRlcyIsImV4dHJhY3RQYXJ0aWNpcGFudHMiLCJkZWNpc2lvbnMiLCJleHRyYWN0RGVjaXNpb25zIiwiYWN0aW9ucyIsImV4dHJhY3RBY3Rpb25zIiwidGV4dCIsInBhdHRlcm5zIiwiZm9yRWFjaCIsInBhdHRlcm4iLCJtYXRjaGVzIiwibmFtZXMiLCJtYXAiLCJuYW1lIiwicHVzaCIsImxlbmd0aCIsInNsaWNlIiwicmVwbGFjZSIsImFjdGlvblBhdHRlcm5zIiwibWF0Y2hBbGwiLCJjaGVja0Nvbm5lY3Rpb24iLCJsaXN0TW9kZWxzIiwibW9kZWxzIiwiY29uc3RydWN0b3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ollama.ts\n"));

/***/ })

});