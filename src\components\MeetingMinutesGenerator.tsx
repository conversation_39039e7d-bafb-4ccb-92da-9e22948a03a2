'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { OllamaService, type MeetingMinutes } from '@/lib/ollama';
import OllamaStatus from '@/components/OllamaStatus';
import { FileText, Upload, Wand2, Download, CheckCircle, AlertCircle } from 'lucide-react';

export default function MeetingMinutesGenerator() {
  const [meetingText, setMeetingText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [meetingMinutes, setMeetingMinutes] = useState<MeetingMinutes | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [demoMode, setDemoMode] = useState(false);
  const [selectedModel, setSelectedModel] = useState('qwq:32b');
  const [currentStep, setCurrentStep] = useState<string>('');
  const [generationProgress, setGenerationProgress] = useState<string[]>([]);
  const [progressValue, setProgressValue] = useState<number>(0);
  const [streamContent, setStreamContent] = useState<string>('');
  const [ollamaService] = useState(() => new OllamaService('http://localhost:11434', selectedModel, demoMode));

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'text/plain') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setMeetingText(content);
      };
      reader.readAsText(file);
    } else {
      setError('请上传 .txt 格式的文件');
    }
  };

  const generateMinutes = async () => {
    if (!meetingText.trim()) {
      setError('请输入会议内容');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setCurrentStep('');
    setGenerationProgress([]);
    setProgressValue(0);
    setStreamContent('');
    setMeetingMinutes(null);

    try {
      // 创建新的服务实例以使用当前的演示模式和模型设置
      const service = new OllamaService('http://localhost:11434', selectedModel, demoMode);

      const minutes = await service.generateMeetingMinutes(
        meetingText,
        (step: string, content: string) => {
          setCurrentStep(step);
          setStreamContent(content);
          setGenerationProgress(prev => {
            if (!prev.includes(step)) {
              const newProgress = [...prev, step];
              setProgressValue((newProgress.length / 6) * 100); // 假设有6个步骤
              return newProgress;
            }
            return prev;
          });
        }
      );

      setCurrentStep('✅ 生成完成！');
      setMeetingMinutes(minutes);
    } catch (err) {
      setError(err instanceof Error ? err.message : '生成会议纪要时发生错误');
      setCurrentStep('');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadMinutes = () => {
    if (!meetingMinutes) return;

    const content = `
# ${meetingMinutes.title}

**日期：** ${meetingMinutes.date}

**参与者：** ${meetingMinutes.participants.join(', ')}

## 会议摘要
${meetingMinutes.summary}

## 关键决策
${meetingMinutes.keyDecisions.map(decision => `- ${decision}`).join('\n')}

## 行动项
${meetingMinutes.actionItems.map(item => 
  `- **${item.task}** (负责人: ${item.assignee}, 优先级: ${item.priority}${item.deadline ? `, 截止日期: ${item.deadline}` : ''})`
).join('\n')}

## 下一步计划
${meetingMinutes.nextSteps.map(step => `- ${step}`).join('\n')}
`;

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${meetingMinutes.title.replace(/\s+/g, '_')}_${meetingMinutes.date}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">智能会议纪要生成器</h1>
        <p className="text-gray-600">使用本地 AI 技术自动生成结构化的会议纪要</p>
        <div className="flex items-center justify-center gap-6 mt-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={demoMode}
              onChange={(e) => setDemoMode(e.target.checked)}
              className="rounded"
            />
            演示模式（无需 Ollama）
          </label>

          {!demoMode && (
            <div className="flex items-center gap-2 text-sm">
              <label htmlFor="model-select">AI 模型:</label>
              <select
                id="model-select"
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="qwq:32b">qwq:32b (推荐)</option>
                <option value="qwen2.5:7b">qwen2.5:7b</option>
                <option value="qwen2.5:0.5b">qwen2.5:0.5b</option>
                <option value="llama3.1:8b">llama3.1:8b</option>
                <option value="llama3.2:3b">llama3.2:3b</option>
              </select>
            </div>
          )}
        </div>
      </div>

      <OllamaStatus demoMode={demoMode} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              会议内容输入
            </CardTitle>
            <CardDescription>
              粘贴会议文本或上传文本文件
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="file-upload" className="block text-sm font-medium mb-2">
                上传文本文件
              </label>
              <Input
                id="file-upload"
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="cursor-pointer"
              />
            </div>
            
            <div className="relative">
              <Textarea
                placeholder="在此粘贴会议内容，包括讨论要点、决策和任务分配..."
                value={meetingText}
                onChange={(e) => setMeetingText(e.target.value)}
                className="min-h-[300px] resize-none"
              />
              <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                {meetingText.length} 字符
              </div>
            </div>

            <Button 
              onClick={generateMinutes}
              disabled={isGenerating || !meetingText.trim()}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Wand2 className="h-4 w-4 mr-2 animate-spin" />
                  正在生成纪要...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  生成会议纪要
                </>
              )}
            </Button>

            {/* 进度显示 */}
            {isGenerating && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md space-y-3">
                <div className="flex items-center gap-2">
                  <Wand2 className="h-4 w-4 text-blue-500 animate-spin" />
                  <span className="text-blue-700 font-medium">正在生成会议纪要</span>
                </div>

                {/* 进度条 */}
                <div className="space-y-2">
                  <Progress value={progressValue} className="h-2" />
                  <div className="text-xs text-blue-600 text-right">
                    {Math.round(progressValue)}%
                  </div>
                </div>

                {/* 当前步骤 */}
                {currentStep && (
                  <div className="text-sm text-blue-600 font-medium">
                    {currentStep}
                  </div>
                )}

                {/* 流式内容预览 */}
                {streamContent && (
                  <div className="bg-white p-3 rounded border text-xs text-gray-600 max-h-20 overflow-y-auto">
                    <div className="font-medium mb-1">AI 生成预览:</div>
                    <div className="whitespace-pre-wrap">{streamContent}</div>
                  </div>
                )}

                {/* 已完成步骤 */}
                {generationProgress.length > 0 && (
                  <div className="space-y-1">
                    <div className="text-xs text-blue-600 font-medium">已完成步骤:</div>
                    {generationProgress.map((step, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs text-green-600">
                        <CheckCircle className="h-3 w-3" />
                        {step}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span className="text-red-700 text-sm">{error}</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 结果显示区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                生成的会议纪要
              </span>
              {meetingMinutes && (
                <Button onClick={downloadMinutes} variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  下载
                </Button>
              )}
            </CardTitle>
            <CardDescription>
              AI 生成的结构化会议纪要
            </CardDescription>
          </CardHeader>
          <CardContent>
            {meetingMinutes ? (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">{meetingMinutes.title}</h3>
                  <p className="text-sm text-gray-600">日期: {meetingMinutes.date}</p>
                  {meetingMinutes.participants.length > 0 && (
                    <p className="text-sm text-gray-600">
                      参与者: {meetingMinutes.participants.join(', ')}
                    </p>
                  )}
                </div>

                <div>
                  <h4 className="font-medium mb-2">会议摘要</h4>
                  <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                    {meetingMinutes.summary}
                  </p>
                </div>

                {meetingMinutes.keyDecisions.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">关键决策</h4>
                    <ul className="space-y-1">
                      {meetingMinutes.keyDecisions.map((decision, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                          <span className="text-blue-500 mt-1">•</span>
                          {decision}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {meetingMinutes.actionItems.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">行动项</h4>
                    <div className="space-y-2">
                      {meetingMinutes.actionItems.map((item, index) => (
                        <div key={index} className="border rounded-md p-3 bg-gray-50">
                          <div className="flex items-start justify-between">
                            <p className="text-sm font-medium text-gray-900">{item.task}</p>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                              {item.priority}
                            </span>
                          </div>
                          <div className="mt-1 text-xs text-gray-600">
                            <span>负责人: {item.assignee}</span>
                            {item.deadline && <span className="ml-3">截止: {item.deadline}</span>}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {meetingMinutes.nextSteps.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">下一步计划</h4>
                    <ul className="space-y-1">
                      {meetingMinutes.nextSteps.map((step, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                          <span className="text-green-500 mt-1">•</span>
                          {step}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>在左侧输入会议内容后，点击生成按钮</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
